"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-switch";
exports.ids = ["vendor-chunks/rc-switch"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-switch/es/index.js":
/*!********************************************!*\
  !*** ./node_modules/rc-switch/es/index.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/KeyCode */ \"(ssr)/./node_modules/rc-util/es/KeyCode.js\");\n\n\n\n\nvar _excluded = [\"prefixCls\", \"className\", \"checked\", \"defaultChecked\", \"disabled\", \"loadingIcon\", \"checkedChildren\", \"unCheckedChildren\", \"onClick\", \"onChange\", \"onKeyDown\"];\n\n\n\n\nvar Switch = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.forwardRef(function (_ref, ref) {\n  var _classNames;\n  var _ref$prefixCls = _ref.prefixCls,\n    prefixCls = _ref$prefixCls === void 0 ? 'rc-switch' : _ref$prefixCls,\n    className = _ref.className,\n    checked = _ref.checked,\n    defaultChecked = _ref.defaultChecked,\n    disabled = _ref.disabled,\n    loadingIcon = _ref.loadingIcon,\n    checkedChildren = _ref.checkedChildren,\n    unCheckedChildren = _ref.unCheckedChildren,\n    onClick = _ref.onClick,\n    onChange = _ref.onChange,\n    onKeyDown = _ref.onKeyDown,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_ref, _excluded);\n  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, {\n      value: checked,\n      defaultValue: defaultChecked\n    }),\n    _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useMergedState, 2),\n    innerChecked = _useMergedState2[0],\n    setInnerChecked = _useMergedState2[1];\n  function triggerChange(newChecked, event) {\n    var mergedChecked = innerChecked;\n    if (!disabled) {\n      mergedChecked = newChecked;\n      setInnerChecked(mergedChecked);\n      onChange === null || onChange === void 0 ? void 0 : onChange(mergedChecked, event);\n    }\n    return mergedChecked;\n  }\n  function onInternalKeyDown(e) {\n    if (e.which === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__[\"default\"].LEFT) {\n      triggerChange(false, e);\n    } else if (e.which === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__[\"default\"].RIGHT) {\n      triggerChange(true, e);\n    }\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n  }\n  function onInternalClick(e) {\n    var ret = triggerChange(!innerChecked, e);\n    // [Legacy] trigger onClick with value\n    onClick === null || onClick === void 0 ? void 0 : onClick(ret, e);\n  }\n  var switchClassName = classnames__WEBPACK_IMPORTED_MODULE_5___default()(prefixCls, className, (_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_classNames, \"\".concat(prefixCls, \"-checked\"), innerChecked), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _classNames));\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(\"button\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restProps, {\n    type: \"button\",\n    role: \"switch\",\n    \"aria-checked\": innerChecked,\n    disabled: disabled,\n    className: switchClassName,\n    ref: ref,\n    onKeyDown: onInternalKeyDown,\n    onClick: onInternalClick\n  }), loadingIcon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-inner\")\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-inner-checked\")\n  }, checkedChildren), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-inner-unchecked\")\n  }, unCheckedChildren)));\n});\nSwitch.displayName = 'Switch';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Switch);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-switch/es/index.js\n");

/***/ })

};
;