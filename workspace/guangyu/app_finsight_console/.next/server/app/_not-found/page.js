/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZfbm90LWZvdW5kJTJGcGFnZSZwYWdlPSUyRl9ub3QtZm91bmQlMkZwYWdlJmFwcFBhdGhzPSZwYWdlUGF0aD0uLiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm5vdC1mb3VuZC1lcnJvci5qcyZhcHBEaXI9JTJGVXNlcnMlMkZoZW5yeWh1YW5nJTJGd29ya3NwYWNlJTJGZ3Vhbmd5dSUyRmFwcF9maW5zaWdodF9jb25zb2xlJTJGc3JjJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj0lMkZVc2VycyUyRmhlbnJ5aHVhbmclMkZ3b3Jrc3BhY2UlMkZndWFuZ3l1JTJGYXBwX2ZpbnNpZ2h0X2NvbnNvbGUmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQztBQUNyQztBQUNBLHNCQUFzQiwwTkFBZ0Y7QUFDdEc7QUFDQTtBQUNBLGFBQWE7QUFDYixXQUFXLElBQUk7QUFDZixTQUFTO0FBQ1Q7QUFDQSx5QkFBeUIsb0pBQWdIO0FBQ3pJLG9CQUFvQiwwTkFBZ0Y7QUFDcEc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLDhHQUFrQjtBQUNqRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9maW5zaWdodC1jb25zb2xlLz9lNjlkIl0sInNvdXJjZXNDb250ZW50IjpbIlwiVFVSQk9QQUNLIHsgdHJhbnNpdGlvbjogbmV4dC1zc3IgfVwiO1xuaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgICBjaGlsZHJlbjogW1wiL19ub3QtZm91bmRcIiwge1xuICAgICAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgICAgICBwYWdlOiBbXG4gICAgICAgICAgICAgICAgKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLFxuICAgICAgICAgICAgICAgIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXG4gICAgICAgICAgICAgIF1cbiAgICAgICAgICAgIH1dXG4gICAgICAgICAgfSwge31dXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9oZW5yeWh1YW5nL3dvcmtzcGFjZS9ndWFuZ3l1L2FwcF9maW5zaWdodF9jb25zb2xlL3NyYy9hcHAvbGF5b3V0LnRzeFwiKSwgXCIvVXNlcnMvaGVucnlodWFuZy93b3Jrc3BhY2UvZ3Vhbmd5dS9hcHBfZmluc2lnaHRfY29uc29sZS9zcmMvYXBwL2xheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvX25vdC1mb3VuZC9wYWdlXCI7XG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL19ub3QtZm91bmQvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvX25vdC1mb3VuZFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fsrc%2Fcomponents%2FProviders%2Findex.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fsrc%2Fcomponents%2FStyledComponentsRegistry%2Findex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fsrc%2Fstyles%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fsrc%2Fcomponents%2FProviders%2Findex.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fsrc%2Fcomponents%2FStyledComponentsRegistry%2Findex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fsrc%2Fstyles%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers/index.tsx */ \"(ssr)/./src/components/Providers/index.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/StyledComponentsRegistry/index.tsx */ \"(ssr)/./src/components/StyledComponentsRegistry/index.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fsrc%2Fcomponents%2FProviders%2Findex.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fsrc%2Fcomponents%2FStyledComponentsRegistry%2Findex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fsrc%2Fstyles%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers/index.tsx":
/*!********************************************!*\
  !*** ./src/components/Providers/index.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ConfigProvider!=!antd */ \"(ssr)/./node_modules/antd/es/config-provider/index.js\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/es/index.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! styled-components */ \"(ssr)/./node_modules/styled-components/dist/styled-components.esm.js\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store */ \"(ssr)/./src/store/index.ts\");\n/* harmony import */ var _hooks_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/redux */ \"(ssr)/./src/hooks/redux.ts\");\n/* harmony import */ var _styles_theme__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/styles/theme */ \"(ssr)/./src/styles/theme.ts\");\n/* harmony import */ var _styles_antdTheme__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/styles/antdTheme */ \"(ssr)/./src/styles/antdTheme.ts\");\n/* harmony import */ var _contexts_PermissionContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/PermissionContext */ \"(ssr)/./src/contexts/PermissionContext.tsx\");\n/* harmony import */ var _store_slices_authSlice__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/store/slices/authSlice */ \"(ssr)/./src/store/slices/authSlice.ts\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(ssr)/./node_modules/antd/lib/locale/zh_CN.js\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\n\n\n\n\n\n\n\n/**\n * 应用初始化组件\n * 负责恢复认证状态等初始化操作\n */ function AppInitializer({ children }) {\n    const dispatch = (0,_hooks_redux__WEBPACK_IMPORTED_MODULE_4__.useAppDispatch)();\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 确保只在客户端执行初始化\n        if (false) {} else {\n            // 服务端渲染时直接标记为已初始化\n            setIsInitialized(true);\n        }\n    }, [\n        dispatch\n    ]);\n    // 在初始化完成前显示加载状态\n    if (!isInitialized) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                height: \"100vh\",\n                background: \"#1a1a1a\",\n                color: \"#ffffff\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"正在初始化...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/Providers/index.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/Providers/index.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n/**\n * 错误边界组件\n * 捕获渲染过程中的错误，防止应用崩溃\n */ class ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"ErrorBoundary 捕获到错误:\", error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    height: \"100vh\",\n                    background: \"#1a1a1a\",\n                    color: \"#ffffff\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"应用出现错误\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/Providers/index.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"请刷新页面重试\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/Providers/index.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        style: {\n                            padding: \"8px 16px\",\n                            marginTop: \"16px\",\n                            background: \"#4f46e5\",\n                            color: \"#fff\",\n                            border: \"none\",\n                            borderRadius: \"4px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"刷新页面\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/Providers/index.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/Providers/index.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n/**\n * 全局Provider组件\n * 提供Redux store、主题配置、权限管理、国际化等\n */ function Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_2__.Provider, {\n            store: _store__WEBPACK_IMPORTED_MODULE_3__.store,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                theme: _styles_antdTheme__WEBPACK_IMPORTED_MODULE_6__.antdTheme,\n                locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                componentSize: \"middle\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_components__WEBPACK_IMPORTED_MODULE_11__.ThemeProvider, {\n                    theme: _styles_theme__WEBPACK_IMPORTED_MODULE_5__.theme,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_PermissionContext__WEBPACK_IMPORTED_MODULE_7__.PermissionProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppInitializer, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/Providers/index.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/Providers/index.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/Providers/index.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/Providers/index.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/Providers/index.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/Providers/index.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/StyledComponentsRegistry/index.tsx":
/*!***********************************************************!*\
  !*** ./src/components/StyledComponentsRegistry/index.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StyledComponentsRegistry)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\n * Styled Components服务端渲染注册组件\n * 简化版本，避免所有Hook相关问题\n */ function StyledComponentsRegistry({ children }) {\n    // 在Next.js 14中，styled-components会自动处理SSR\n    // 我们只需要简单地返回children即可\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TdHlsZWRDb21wb25lbnRzUmVnaXN0cnkvaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUV5QjtBQUV6Qjs7O0NBR0MsR0FDYyxTQUFTQyx5QkFBeUIsRUFDL0NDLFFBQVEsRUFHVDtJQUNDLHlDQUF5QztJQUN6Qyx1QkFBdUI7SUFDdkIscUJBQU87a0JBQUdBOztBQUNaIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmluc2lnaHQtY29uc29sZS8uL3NyYy9jb21wb25lbnRzL1N0eWxlZENvbXBvbmVudHNSZWdpc3RyeS9pbmRleC50c3g/Mjg5MCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuXG4vKipcbiAqIFN0eWxlZCBDb21wb25lbnRz5pyN5Yqh56uv5riy5p+T5rOo5YaM57uE5Lu2XG4gKiDnroDljJbniYjmnKzvvIzpgb/lhY3miYDmnIlIb29r55u45YWz6Zeu6aKYXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFN0eWxlZENvbXBvbmVudHNSZWdpc3RyeSh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIC8vIOWcqE5leHQuanMgMTTkuK3vvIxzdHlsZWQtY29tcG9uZW50c+S8muiHquWKqOWkhOeQhlNTUlxuICAvLyDmiJHku6zlj6rpnIDopoHnroDljZXlnLDov5Tlm55jaGlsZHJlbuWNs+WPr1xuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+XG59ICJdLCJuYW1lcyI6WyJSZWFjdCIsIlN0eWxlZENvbXBvbmVudHNSZWdpc3RyeSIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/StyledComponentsRegistry/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/constants/index.ts":
/*!********************************!*\
  !*** ./src/constants/index.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API: () => (/* binding */ API),\n/* harmony export */   APP: () => (/* binding */ APP),\n/* harmony export */   AUTH: () => (/* binding */ AUTH),\n/* harmony export */   PERMISSIONS: () => (/* binding */ PERMISSIONS),\n/* harmony export */   ROLES: () => (/* binding */ ROLES),\n/* harmony export */   ROUTES: () => (/* binding */ ROUTES),\n/* harmony export */   UI: () => (/* binding */ UI)\n/* harmony export */ });\n/**\n * 应用常量配置\n */ // ==================== 路由常量 ====================\nconst ROUTES = {\n    // 认证相关\n    LOGIN: \"/admin/login\",\n    // 仪表板\n    DASHBOARD: \"/dashboard\",\n    // 用户管理 - 默认登录后跳转页面\n    USERS: \"/admin/users\",\n    USER_DETAIL: (id)=>`/admin/users/${id}`,\n    USER_EDIT: (id)=>`/admin/users/${id}/edit`,\n    USER_CREATE: \"/admin/users/create\",\n    // 角色管理\n    ROLES: \"/admin/roles\",\n    ROLE_DETAIL: (id)=>`/admin/roles/${id}`,\n    ROLE_EDIT: (id)=>`/admin/roles/${id}/edit`,\n    ROLE_CREATE: \"/admin/roles/create\",\n    // 权限管理\n    PERMISSIONS: \"/admin/permissions\",\n    PERMISSION_DETAIL: (id)=>`/admin/permissions/${id}`,\n    PERMISSION_EDIT: (id)=>`/admin/permissions/${id}/edit`,\n    PERMISSION_CREATE: \"/admin/permissions/create\",\n    // 默认登录后跳转的页面\n    DEFAULT_REDIRECT: \"/admin/users\",\n    // 新增权限相关路由\n    HOME: \"/\",\n    ADMIN_LOGIN: \"/admin/login\",\n    ADMIN_DASHBOARD: \"/dashboard\",\n    ADMIN_USERS: \"/admin/users\",\n    ADMIN_USERS_CREATE: \"/admin/users/create\",\n    ADMIN_USERS_EDIT: (id)=>`/admin/users/${id}/edit`,\n    ADMIN_USERS_DETAIL: (id)=>`/admin/users/${id}`,\n    ADMIN_USERS_ROLES: (id)=>`/admin/users/${id}/roles`,\n    ADMIN_ROLES: \"/admin/roles\",\n    ADMIN_ROLES_CREATE: \"/admin/roles/create\",\n    ADMIN_ROLES_EDIT: (id)=>`/admin/roles/${id}/edit`,\n    ADMIN_ROLES_DETAIL: (id)=>`/admin/roles/${id}`,\n    ADMIN_PERMISSIONS: \"/admin/permissions\",\n    ADMIN_PERMISSIONS_CREATE: \"/admin/permissions/create\",\n    ADMIN_PERMISSIONS_EDIT: (id)=>`/admin/permissions/${id}/edit`,\n    ADMIN_PERMISSIONS_DETAIL: (id)=>`/admin/permissions/${id}`,\n    ADMIN_DEBUG_PERMISSIONS: \"/admin/debug/permissions\",\n    // 数据处理规则管理\n    ADMIN_DATA_PROCESSING_RULES: \"/admin/data-processing-rules\",\n    ADMIN_DATA_PROCESSING_PIPELINES: \"/admin/data-processing-pipelines\",\n    // 快讯管理\n    ADMIN_FLASH_NEWS: \"/admin/data-processing/flash-news\"\n};\n// ==================== 认证常量 ====================\nconst AUTH = {\n    // 短信验证码倒计时时间（秒）\n    SMS_RESEND_COUNTDOWN: 60,\n    // 验证码长度\n    SMS_CODE_LENGTH: 6,\n    // Token存储key - 与userAPI中使用的保持一致\n    TOKEN_STORAGE_KEY: \"access_token\",\n    USER_STORAGE_KEY: \"user\",\n    // 手机号正则\n    PHONE_REGEX: /^1[3-9]\\d{9}$/,\n    // 新增令牌类型和令牌过期时间\n    TOKEN_TYPE: \"Bearer\",\n    TOKEN_EXPIRY: 24 * 60 * 60 * 1000\n};\n// ==================== 应用信息 ====================\nconst APP = {\n    NAME: process.env.NEXT_PUBLIC_APP_NAME || \"FinSight控制台\",\n    DESCRIPTION: process.env.NEXT_PUBLIC_APP_DESCRIPTION || \"金融洞察管理平台\",\n    VERSION: process.env.NEXT_PUBLIC_APP_VERSION || \"1.0.0\"\n};\n// ==================== API常量 ====================\nconst API = {\n    BASE_URL: \"http://helloapi.lightrain.vip\",\n    TIMEOUT: 10000,\n    RETRY_TIMES: 3\n};\n// ==================== UI常量 ====================\nconst UI = {\n    // 分页配置\n    PAGE_SIZE_OPTIONS: [\n        \"10\",\n        \"20\",\n        \"50\",\n        \"100\"\n    ],\n    DEFAULT_PAGE_SIZE: 20,\n    // 表格配置\n    TABLE_SCROLL_Y: 400,\n    // 响应式断点\n    BREAKPOINTS: {\n        xs: 480,\n        sm: 576,\n        md: 768,\n        lg: 992,\n        xl: 1200,\n        xxl: 1600\n    }\n};\n// ==================== 权限常量 ====================\nconst PERMISSIONS = {\n    // 用户管理权限\n    USER_LIST_READ: \"user.list.read\",\n    USER_PROFILE_READ: \"user.profile.read\",\n    USER_CREATE: \"user.user.create\",\n    USER_UPDATE: \"user.user.update\",\n    USER_DELETE: \"user.user.delete\",\n    USER_STATUS_UPDATE: \"user.status.update\",\n    USER_ROLE_ASSIGN: \"user.role.assign\",\n    USER_PERMISSION_READ: \"user.permission.read\",\n    USER_ROLE_BATCH_ASSIGN: \"user.role.batch.assign\",\n    // 权限管理模块\n    PERMISSION: {\n        CREATE: \"permission.permission.create\",\n        READ: \"permission.permission.read\",\n        UPDATE: \"permission.permission.update\",\n        DELETE: \"permission.permission.delete\",\n        LIST_READ: \"permission.list.read\",\n        ANALYTICS_READ: \"permission.analytics.read\"\n    },\n    // 角色管理模块  \n    ROLE: {\n        CREATE: \"role.role.create\",\n        READ: \"role.role.read\",\n        UPDATE: \"role.role.update\",\n        DELETE: \"role.role.delete\",\n        LIST_READ: \"role.list.read\",\n        PERMISSION_ASSIGN: \"role.permission.assign\",\n        PERMISSION_REVOKE: \"role.permission.revoke\",\n        PERMISSION_READ: \"role.permission.read\",\n        ANALYTICS_READ: \"role.analytics.read\"\n    },\n    // 标签管理模块\n    TAG: {\n        CREATE: \"tag.tag.create\",\n        READ: \"tag.tag.read\",\n        UPDATE: \"tag.tag.update\",\n        DELETE: \"tag.tag.delete\",\n        LIST_READ: \"tag.list.read\",\n        CLASSIFICATION_CREATE: \"tag.classification.create\",\n        CLASSIFICATION_READ: \"tag.classification.read\",\n        CLASSIFICATION_UPDATE: \"tag.classification.update\",\n        CLASSIFICATION_DELETE: \"tag.classification.delete\",\n        ANALYTICS_READ: \"tag.analytics.read\"\n    },\n    // 分类管理模块\n    CLASSIFICATION: {\n        DIMENSION_CREATE: \"classification.dimension.create\",\n        DIMENSION_READ: \"classification.dimension.read\",\n        DIMENSION_UPDATE: \"classification.dimension.update\",\n        DIMENSION_DELETE: \"classification.dimension.delete\",\n        VALUE_CREATE: \"classification.value.create\",\n        VALUE_READ: \"classification.value.read\",\n        VALUE_UPDATE: \"classification.value.update\",\n        VALUE_DELETE: \"classification.value.delete\",\n        ANALYTICS_READ: \"classification.analytics.read\"\n    },\n    // 分类管理模块\n    CLASSIFICATION: {\n        CREATE: \"classification.classification.create\",\n        READ: \"classification.classification.read\",\n        UPDATE: \"classification.classification.update\",\n        DELETE: \"classification.classification.delete\",\n        LIST_READ: \"classification.list.read\",\n        DIMENSION_CREATE: \"classification.dimension.create\",\n        DIMENSION_READ: \"classification.dimension.read\",\n        DIMENSION_UPDATE: \"classification.dimension.update\",\n        DIMENSION_DELETE: \"classification.dimension.delete\",\n        VALUE_CREATE: \"classification.value.create\",\n        VALUE_READ: \"classification.value.read\",\n        VALUE_UPDATE: \"classification.value.update\",\n        VALUE_DELETE: \"classification.value.delete\",\n        ANALYTICS_READ: \"classification.analytics.read\"\n    },\n    // 数据源管理\n    DATA_SOURCE: {\n        CREATE: \"data_source.data_source.create\",\n        READ: \"data_source.data_source.read\",\n        UPDATE: \"data_source.data_source.update\",\n        DELETE: \"data_source.data_source.delete\",\n        LIST_READ: \"data_source.list.read\",\n        STATS_READ: \"data_source.stats.read\",\n        CONFIG_CREATE: \"data_source.config.create\",\n        CONFIG_READ: \"data_source.config.read\",\n        CONFIG_LIST_READ: \"data_source.config.read\",\n        CONFIG_UPDATE: \"data_source.config.update\",\n        CONFIG_DELETE: \"data_source.config.delete\",\n        CONFIG_MANAGE: \"data_source.config.manage\"\n    },\n    // 原始数据记录模块\n    RAW_DATA_RECORD: {\n        CREATE: \"raw_data_record.raw_data_record.create\",\n        READ: \"raw_data_record.raw_data_record.read\",\n        UPDATE: \"raw_data_record.raw_data_record.update\",\n        DELETE: \"raw_data_record.raw_data_record.delete\",\n        MANAGE: \"raw_data_record.raw_data_record.manage\",\n        ANALYZE: \"raw_data_record.raw_data_record.analyze\",\n        LIST_READ: \"raw_data_record.list.read\",\n        STATS_READ: \"raw_data_record.stats.read\",\n        ARCHIVE: \"raw_data_record.raw_data_record.archive\",\n        BATCH_UPDATE: \"raw_data_record.raw_data_record.batch_update\",\n        DUPLICATE_READ: \"raw_data_record.duplicate.read\"\n    },\n    // 数据处理管道模块\n    DATA_PROCESSING_PIPELINE: {\n        CREATE: \"data_processing.pipeline.create\",\n        READ: \"data_processing.pipeline.read\",\n        UPDATE: \"data_processing.pipeline.update\",\n        DELETE: \"data_processing.pipeline.delete\",\n        LIST_READ: \"data_processing.pipeline.read\",\n        STATS_READ: \"data_processing.pipeline.read\",\n        MANAGE: \"data_processing.pipeline.manage\",\n        ACTIVE_PIPELINES_READ: \"data_processing.pipeline.read\"\n    },\n    // 快讯管理模块\n    FLASH_NEWS: {\n        READ: \"flash_news.flash_news.read\",\n        UPDATE: \"flash_news.flash_news.update\",\n        DELETE: \"flash_news.flash_news.delete\",\n        STATS: \"flash_news.flash_news.stats\"\n    }\n};\n// ==================== 角色常量 ====================\nconst ROLES = {\n    ADMIN: \"admin\",\n    ACCOUNT_MANAGER: \"account_manager\",\n    RISK_OFFICER: \"risk_officer\",\n    USER: \"user\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29uc3RhbnRzL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTs7Q0FFQyxHQUVELGlEQUFpRDtBQUMxQyxNQUFNQSxTQUFTO0lBQ3BCLE9BQU87SUFDUEMsT0FBTztJQUVQLE1BQU07SUFDTkMsV0FBVztJQUVYLG1CQUFtQjtJQUNuQkMsT0FBTztJQUNQQyxhQUFhLENBQUNDLEtBQWUsQ0FBQyxhQUFhLEVBQUVBLEdBQUcsQ0FBQztJQUNqREMsV0FBVyxDQUFDRCxLQUFlLENBQUMsYUFBYSxFQUFFQSxHQUFHLEtBQUssQ0FBQztJQUNwREUsYUFBYTtJQUViLE9BQU87SUFDUEMsT0FBTztJQUNQQyxhQUFhLENBQUNKLEtBQWUsQ0FBQyxhQUFhLEVBQUVBLEdBQUcsQ0FBQztJQUNqREssV0FBVyxDQUFDTCxLQUFlLENBQUMsYUFBYSxFQUFFQSxHQUFHLEtBQUssQ0FBQztJQUNwRE0sYUFBYTtJQUViLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxtQkFBbUIsQ0FBQ1IsS0FBZSxDQUFDLG1CQUFtQixFQUFFQSxHQUFHLENBQUM7SUFDN0RTLGlCQUFpQixDQUFDVCxLQUFlLENBQUMsbUJBQW1CLEVBQUVBLEdBQUcsS0FBSyxDQUFDO0lBQ2hFVSxtQkFBbUI7SUFFbkIsYUFBYTtJQUNiQyxrQkFBa0I7SUFFbEIsV0FBVztJQUNYQyxNQUFNO0lBQ05DLGFBQWE7SUFDYkMsaUJBQWlCO0lBQ2pCQyxhQUFhO0lBQ2JDLG9CQUFvQjtJQUNwQkMsa0JBQWtCLENBQUNqQixLQUF3QixDQUFDLGFBQWEsRUFBRUEsR0FBRyxLQUFLLENBQUM7SUFDcEVrQixvQkFBb0IsQ0FBQ2xCLEtBQXdCLENBQUMsYUFBYSxFQUFFQSxHQUFHLENBQUM7SUFDakVtQixtQkFBbUIsQ0FBQ25CLEtBQXdCLENBQUMsYUFBYSxFQUFFQSxHQUFHLE1BQU0sQ0FBQztJQUN0RW9CLGFBQWE7SUFDYkMsb0JBQW9CO0lBQ3BCQyxrQkFBa0IsQ0FBQ3RCLEtBQXdCLENBQUMsYUFBYSxFQUFFQSxHQUFHLEtBQUssQ0FBQztJQUNwRXVCLG9CQUFvQixDQUFDdkIsS0FBd0IsQ0FBQyxhQUFhLEVBQUVBLEdBQUcsQ0FBQztJQUNqRXdCLG1CQUFtQjtJQUNuQkMsMEJBQTBCO0lBQzFCQyx3QkFBd0IsQ0FBQzFCLEtBQXdCLENBQUMsbUJBQW1CLEVBQUVBLEdBQUcsS0FBSyxDQUFDO0lBQ2hGMkIsMEJBQTBCLENBQUMzQixLQUF3QixDQUFDLG1CQUFtQixFQUFFQSxHQUFHLENBQUM7SUFDN0U0Qix5QkFBeUI7SUFFekIsV0FBVztJQUNYQyw2QkFBNkI7SUFDN0JDLGlDQUFpQztJQUVqQyxPQUFPO0lBQ1BDLGtCQUFrQjtBQUNwQixFQUFVO0FBRVYsaURBQWlEO0FBQzFDLE1BQU1DLE9BQU87SUFDbEIsZ0JBQWdCO0lBQ2hCQyxzQkFBc0I7SUFFdEIsUUFBUTtJQUNSQyxpQkFBaUI7SUFFakIsZ0NBQWdDO0lBQ2hDQyxtQkFBbUI7SUFDbkJDLGtCQUFrQjtJQUVsQixRQUFRO0lBQ1JDLGFBQWE7SUFFYixnQkFBZ0I7SUFDaEJDLFlBQVk7SUFDWkMsY0FBYyxLQUFLLEtBQUssS0FBSztBQUMvQixFQUFVO0FBRVYsaURBQWlEO0FBQzFDLE1BQU1DLE1BQU07SUFDakJDLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQ0Msb0JBQW9CLElBQUk7SUFDMUNDLGFBQWFILFFBQVFDLEdBQUcsQ0FBQ0csMkJBQTJCLElBQUk7SUFDeERDLFNBQVNMLFFBQVFDLEdBQUcsQ0FBQ0ssdUJBQXVCLElBQUk7QUFDbEQsRUFBVTtBQUVWLGtEQUFrRDtBQUMzQyxNQUFNQyxNQUFNO0lBQ2pCQyxVQUFVO0lBQ1ZDLFNBQVM7SUFDVEMsYUFBYTtBQUNmLEVBQVU7QUFFVixpREFBaUQ7QUFDMUMsTUFBTUMsS0FBSztJQUNoQixPQUFPO0lBQ1BDLG1CQUFtQjtRQUFDO1FBQU07UUFBTTtRQUFNO0tBQU07SUFDNUNDLG1CQUFtQjtJQUVuQixPQUFPO0lBQ1BDLGdCQUFnQjtJQUVoQixRQUFRO0lBQ1JDLGFBQWE7UUFDWEMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLEtBQUs7SUFDUDtBQUNGLEVBQVU7QUFFVixpREFBaUQ7QUFDMUMsTUFBTXhELGNBQWM7SUFDekIsU0FBUztJQUNUeUQsZ0JBQWdCO0lBQ2hCQyxtQkFBbUI7SUFDbkIvRCxhQUFhO0lBQ2JnRSxhQUFhO0lBQ2JDLGFBQWE7SUFDYkMsb0JBQW9CO0lBQ3BCQyxrQkFBa0I7SUFDbEJDLHNCQUFzQjtJQUN0QkMsd0JBQXdCO0lBRXhCLFNBQVM7SUFDVEMsWUFBWTtRQUNWQyxRQUFRO1FBQ1JDLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxRQUFRO1FBQ1JDLFdBQVc7UUFDWEMsZ0JBQWdCO0lBQ2xCO0lBRUEsV0FBVztJQUNYQyxNQUFNO1FBQ0pOLFFBQVE7UUFDUkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLFFBQVE7UUFDUkMsV0FBVztRQUNYRyxtQkFBbUI7UUFDbkJDLG1CQUFtQjtRQUNuQkMsaUJBQWlCO1FBQ2pCSixnQkFBZ0I7SUFDbEI7SUFFQSxTQUFTO0lBQ1RLLEtBQUs7UUFDSFYsUUFBUTtRQUNSQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsUUFBUTtRQUNSQyxXQUFXO1FBQ1hPLHVCQUF1QjtRQUN2QkMscUJBQXFCO1FBQ3JCQyx1QkFBdUI7UUFDdkJDLHVCQUF1QjtRQUN2QlQsZ0JBQWdCO0lBQ2xCO0lBRUEsU0FBUztJQUNUVSxnQkFBZ0I7UUFDZEMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7UUFDaEJDLGtCQUFrQjtRQUNsQkMsa0JBQWtCO1FBQ2xCQyxjQUFjO1FBQ2RDLFlBQVk7UUFDWkMsY0FBYztRQUNkQyxjQUFjO1FBQ2RsQixnQkFBZ0I7SUFDbEI7SUFFQSxTQUFTO0lBQ1RVLGdCQUFnQjtRQUNkZixRQUFRO1FBQ1JDLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxRQUFRO1FBQ1JDLFdBQVc7UUFDWFksa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7UUFDaEJDLGtCQUFrQjtRQUNsQkMsa0JBQWtCO1FBQ2xCQyxjQUFjO1FBQ2RDLFlBQVk7UUFDWkMsY0FBYztRQUNkQyxjQUFjO1FBQ2RsQixnQkFBZ0I7SUFDbEI7SUFHQSxRQUFRO0lBQ1JtQixhQUFhO1FBQ1h4QixRQUFRO1FBQ1JDLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxRQUFRO1FBQ1JDLFdBQVc7UUFDWHFCLFlBQVk7UUFDWkMsZUFBZTtRQUNmQyxhQUFhO1FBQ2JDLGtCQUFrQjtRQUNsQkMsZUFBZTtRQUNmQyxlQUFlO1FBQ2ZDLGVBQWU7SUFDakI7SUFFQSxXQUFXO0lBQ1hDLGlCQUFpQjtRQUNmaEMsUUFBUTtRQUNSQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsUUFBUTtRQUNSOEIsUUFBUTtRQUNSQyxTQUFTO1FBQ1Q5QixXQUFXO1FBQ1hxQixZQUFZO1FBQ1pVLFNBQVM7UUFDVEMsY0FBYztRQUNkQyxnQkFBZ0I7SUFDbEI7SUFFQSxXQUFXO0lBQ1hDLDBCQUEwQjtRQUN4QnRDLFFBQVE7UUFDUkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLFFBQVE7UUFDUkMsV0FBVztRQUNYcUIsWUFBWTtRQUNaUSxRQUFRO1FBQ1JNLHVCQUF1QjtJQUN6QjtJQUVBLFNBQVM7SUFDVEMsWUFBWTtRQUNWdkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLFFBQVE7UUFDUnNDLE9BQU87SUFDVDtBQUNGLEVBQVU7QUFFVixpREFBaUQ7QUFDMUMsTUFBTS9HLFFBQVE7SUFDbkJnSCxPQUFPO0lBQ1BDLGlCQUFpQjtJQUNqQkMsY0FBYztJQUNkQyxNQUFNO0FBQ1IsRUFBVSIsInNvdXJjZXMiOlsid2VicGFjazovL2ZpbnNpZ2h0LWNvbnNvbGUvLi9zcmMvY29uc3RhbnRzL2luZGV4LnRzPzYzZmUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiDlupTnlKjluLjph4/phY3nva5cbiAqL1xuXG4vLyA9PT09PT09PT09PT09PT09PT09PSDot6/nlLHluLjph48gPT09PT09PT09PT09PT09PT09PT1cbmV4cG9ydCBjb25zdCBST1VURVMgPSB7XG4gIC8vIOiupOivgeebuOWFs1xuICBMT0dJTjogJy9hZG1pbi9sb2dpbicsXG4gIFxuICAvLyDku6rooajmnb9cbiAgREFTSEJPQVJEOiAnL2Rhc2hib2FyZCcsXG4gIFxuICAvLyDnlKjmiLfnrqHnkIYgLSDpu5jorqTnmbvlvZXlkI7ot7PovazpobXpnaJcbiAgVVNFUlM6ICcvYWRtaW4vdXNlcnMnLFxuICBVU0VSX0RFVEFJTDogKGlkOiBzdHJpbmcpID0+IGAvYWRtaW4vdXNlcnMvJHtpZH1gLFxuICBVU0VSX0VESVQ6IChpZDogc3RyaW5nKSA9PiBgL2FkbWluL3VzZXJzLyR7aWR9L2VkaXRgLFxuICBVU0VSX0NSRUFURTogJy9hZG1pbi91c2Vycy9jcmVhdGUnLFxuICBcbiAgLy8g6KeS6Imy566h55CGXG4gIFJPTEVTOiAnL2FkbWluL3JvbGVzJyxcbiAgUk9MRV9ERVRBSUw6IChpZDogc3RyaW5nKSA9PiBgL2FkbWluL3JvbGVzLyR7aWR9YCxcbiAgUk9MRV9FRElUOiAoaWQ6IHN0cmluZykgPT4gYC9hZG1pbi9yb2xlcy8ke2lkfS9lZGl0YCxcbiAgUk9MRV9DUkVBVEU6ICcvYWRtaW4vcm9sZXMvY3JlYXRlJyxcbiAgXG4gIC8vIOadg+mZkOeuoeeQhlxuICBQRVJNSVNTSU9OUzogJy9hZG1pbi9wZXJtaXNzaW9ucycsXG4gIFBFUk1JU1NJT05fREVUQUlMOiAoaWQ6IHN0cmluZykgPT4gYC9hZG1pbi9wZXJtaXNzaW9ucy8ke2lkfWAsXG4gIFBFUk1JU1NJT05fRURJVDogKGlkOiBzdHJpbmcpID0+IGAvYWRtaW4vcGVybWlzc2lvbnMvJHtpZH0vZWRpdGAsXG4gIFBFUk1JU1NJT05fQ1JFQVRFOiAnL2FkbWluL3Blcm1pc3Npb25zL2NyZWF0ZScsXG4gIFxuICAvLyDpu5jorqTnmbvlvZXlkI7ot7PovaznmoTpobXpnaJcbiAgREVGQVVMVF9SRURJUkVDVDogJy9hZG1pbi91c2VycycsXG5cbiAgLy8g5paw5aKe5p2D6ZmQ55u45YWz6Lev55SxXG4gIEhPTUU6ICcvJyxcbiAgQURNSU5fTE9HSU46ICcvYWRtaW4vbG9naW4nLFxuICBBRE1JTl9EQVNIQk9BUkQ6ICcvZGFzaGJvYXJkJyxcbiAgQURNSU5fVVNFUlM6ICcvYWRtaW4vdXNlcnMnLFxuICBBRE1JTl9VU0VSU19DUkVBVEU6ICcvYWRtaW4vdXNlcnMvY3JlYXRlJyxcbiAgQURNSU5fVVNFUlNfRURJVDogKGlkOiBudW1iZXIgfCBzdHJpbmcpID0+IGAvYWRtaW4vdXNlcnMvJHtpZH0vZWRpdGAsXG4gIEFETUlOX1VTRVJTX0RFVEFJTDogKGlkOiBudW1iZXIgfCBzdHJpbmcpID0+IGAvYWRtaW4vdXNlcnMvJHtpZH1gLFxuICBBRE1JTl9VU0VSU19ST0xFUzogKGlkOiBudW1iZXIgfCBzdHJpbmcpID0+IGAvYWRtaW4vdXNlcnMvJHtpZH0vcm9sZXNgLFxuICBBRE1JTl9ST0xFUzogJy9hZG1pbi9yb2xlcycsXG4gIEFETUlOX1JPTEVTX0NSRUFURTogJy9hZG1pbi9yb2xlcy9jcmVhdGUnLFxuICBBRE1JTl9ST0xFU19FRElUOiAoaWQ6IG51bWJlciB8IHN0cmluZykgPT4gYC9hZG1pbi9yb2xlcy8ke2lkfS9lZGl0YCxcbiAgQURNSU5fUk9MRVNfREVUQUlMOiAoaWQ6IG51bWJlciB8IHN0cmluZykgPT4gYC9hZG1pbi9yb2xlcy8ke2lkfWAsXG4gIEFETUlOX1BFUk1JU1NJT05TOiAnL2FkbWluL3Blcm1pc3Npb25zJyxcbiAgQURNSU5fUEVSTUlTU0lPTlNfQ1JFQVRFOiAnL2FkbWluL3Blcm1pc3Npb25zL2NyZWF0ZScsXG4gIEFETUlOX1BFUk1JU1NJT05TX0VESVQ6IChpZDogbnVtYmVyIHwgc3RyaW5nKSA9PiBgL2FkbWluL3Blcm1pc3Npb25zLyR7aWR9L2VkaXRgLFxuICBBRE1JTl9QRVJNSVNTSU9OU19ERVRBSUw6IChpZDogbnVtYmVyIHwgc3RyaW5nKSA9PiBgL2FkbWluL3Blcm1pc3Npb25zLyR7aWR9YCxcbiAgQURNSU5fREVCVUdfUEVSTUlTU0lPTlM6ICcvYWRtaW4vZGVidWcvcGVybWlzc2lvbnMnLFxuICBcbiAgLy8g5pWw5o2u5aSE55CG6KeE5YiZ566h55CGXG4gIEFETUlOX0RBVEFfUFJPQ0VTU0lOR19SVUxFUzogJy9hZG1pbi9kYXRhLXByb2Nlc3NpbmctcnVsZXMnLFxuICBBRE1JTl9EQVRBX1BST0NFU1NJTkdfUElQRUxJTkVTOiAnL2FkbWluL2RhdGEtcHJvY2Vzc2luZy1waXBlbGluZXMnLFxuXG4gIC8vIOW/q+iur+euoeeQhlxuICBBRE1JTl9GTEFTSF9ORVdTOiAnL2FkbWluL2RhdGEtcHJvY2Vzc2luZy9mbGFzaC1uZXdzJyxcbn0gYXMgY29uc3RcblxuLy8gPT09PT09PT09PT09PT09PT09PT0g6K6k6K+B5bi46YePID09PT09PT09PT09PT09PT09PT09XG5leHBvcnQgY29uc3QgQVVUSCA9IHtcbiAgLy8g55+t5L+h6aqM6K+B56CB5YCS6K6h5pe25pe26Ze077yI56eS77yJXG4gIFNNU19SRVNFTkRfQ09VTlRET1dOOiA2MCxcbiAgXG4gIC8vIOmqjOivgeeggemVv+W6plxuICBTTVNfQ09ERV9MRU5HVEg6IDYsXG4gIFxuICAvLyBUb2tlbuWtmOWCqGtleSAtIOS4jnVzZXJBUEnkuK3kvb/nlKjnmoTkv53mjIHkuIDoh7RcbiAgVE9LRU5fU1RPUkFHRV9LRVk6ICdhY2Nlc3NfdG9rZW4nLFxuICBVU0VSX1NUT1JBR0VfS0VZOiAndXNlcicsXG4gIFxuICAvLyDmiYvmnLrlj7fmraPliJlcbiAgUEhPTkVfUkVHRVg6IC9eMVszLTldXFxkezl9JC8sXG4gIFxuICAvLyDmlrDlop7ku6TniYznsbvlnovlkozku6TniYzov4fmnJ/ml7bpl7RcbiAgVE9LRU5fVFlQRTogJ0JlYXJlcicsXG4gIFRPS0VOX0VYUElSWTogMjQgKiA2MCAqIDYwICogMTAwMCwgLy8gMjTlsI/ml7Zcbn0gYXMgY29uc3RcblxuLy8gPT09PT09PT09PT09PT09PT09PT0g5bqU55So5L+h5oGvID09PT09PT09PT09PT09PT09PT09XG5leHBvcnQgY29uc3QgQVBQID0ge1xuICBOQU1FOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUFBfTkFNRSB8fCAnRmluU2lnaHTmjqfliLblj7AnLFxuICBERVNDUklQVElPTjogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBQX0RFU0NSSVBUSU9OIHx8ICfph5Hono3mtJ7lr5/nrqHnkIblubPlj7AnLFxuICBWRVJTSU9OOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUFBfVkVSU0lPTiB8fCAnMS4wLjAnLFxufSBhcyBjb25zdFxuXG4vLyA9PT09PT09PT09PT09PT09PT09PSBBUEnluLjph48gPT09PT09PT09PT09PT09PT09PT1cbmV4cG9ydCBjb25zdCBBUEkgPSB7XG4gIEJBU0VfVVJMOiAnaHR0cDovL2hlbGxvYXBpLmxpZ2h0cmFpbi52aXAnLFxuICBUSU1FT1VUOiAxMDAwMCxcbiAgUkVUUllfVElNRVM6IDMsXG59IGFzIGNvbnN0XG5cbi8vID09PT09PT09PT09PT09PT09PT09IFVJ5bi46YePID09PT09PT09PT09PT09PT09PT09XG5leHBvcnQgY29uc3QgVUkgPSB7XG4gIC8vIOWIhumhtemFjee9rlxuICBQQUdFX1NJWkVfT1BUSU9OUzogWycxMCcsICcyMCcsICc1MCcsICcxMDAnXSxcbiAgREVGQVVMVF9QQUdFX1NJWkU6IDIwLFxuICBcbiAgLy8g6KGo5qC86YWN572uXG4gIFRBQkxFX1NDUk9MTF9ZOiA0MDAsXG4gIFxuICAvLyDlk43lupTlvI/mlq3ngrlcbiAgQlJFQUtQT0lOVFM6IHtcbiAgICB4czogNDgwLFxuICAgIHNtOiA1NzYsXG4gICAgbWQ6IDc2OCxcbiAgICBsZzogOTkyLFxuICAgIHhsOiAxMjAwLFxuICAgIHh4bDogMTYwMCxcbiAgfSxcbn0gYXMgY29uc3RcblxuLy8gPT09PT09PT09PT09PT09PT09PT0g5p2D6ZmQ5bi46YePID09PT09PT09PT09PT09PT09PT09XG5leHBvcnQgY29uc3QgUEVSTUlTU0lPTlMgPSB7XG4gIC8vIOeUqOaIt+euoeeQhuadg+mZkFxuICBVU0VSX0xJU1RfUkVBRDogJ3VzZXIubGlzdC5yZWFkJyxcbiAgVVNFUl9QUk9GSUxFX1JFQUQ6ICd1c2VyLnByb2ZpbGUucmVhZCcsXG4gIFVTRVJfQ1JFQVRFOiAndXNlci51c2VyLmNyZWF0ZScsXG4gIFVTRVJfVVBEQVRFOiAndXNlci51c2VyLnVwZGF0ZScsXG4gIFVTRVJfREVMRVRFOiAndXNlci51c2VyLmRlbGV0ZScsXG4gIFVTRVJfU1RBVFVTX1VQREFURTogJ3VzZXIuc3RhdHVzLnVwZGF0ZScsXG4gIFVTRVJfUk9MRV9BU1NJR046ICd1c2VyLnJvbGUuYXNzaWduJyxcbiAgVVNFUl9QRVJNSVNTSU9OX1JFQUQ6ICd1c2VyLnBlcm1pc3Npb24ucmVhZCcsXG4gIFVTRVJfUk9MRV9CQVRDSF9BU1NJR046ICd1c2VyLnJvbGUuYmF0Y2guYXNzaWduJyxcblxuICAvLyDmnYPpmZDnrqHnkIbmqKHlnZdcbiAgUEVSTUlTU0lPTjoge1xuICAgIENSRUFURTogJ3Blcm1pc3Npb24ucGVybWlzc2lvbi5jcmVhdGUnLFxuICAgIFJFQUQ6ICdwZXJtaXNzaW9uLnBlcm1pc3Npb24ucmVhZCcsXG4gICAgVVBEQVRFOiAncGVybWlzc2lvbi5wZXJtaXNzaW9uLnVwZGF0ZScsXG4gICAgREVMRVRFOiAncGVybWlzc2lvbi5wZXJtaXNzaW9uLmRlbGV0ZScsXG4gICAgTElTVF9SRUFEOiAncGVybWlzc2lvbi5saXN0LnJlYWQnLFxuICAgIEFOQUxZVElDU19SRUFEOiAncGVybWlzc2lvbi5hbmFseXRpY3MucmVhZCdcbiAgfSxcblxuICAvLyDop5LoibLnrqHnkIbmqKHlnZcgIFxuICBST0xFOiB7XG4gICAgQ1JFQVRFOiAncm9sZS5yb2xlLmNyZWF0ZScsXG4gICAgUkVBRDogJ3JvbGUucm9sZS5yZWFkJyxcbiAgICBVUERBVEU6ICdyb2xlLnJvbGUudXBkYXRlJyxcbiAgICBERUxFVEU6ICdyb2xlLnJvbGUuZGVsZXRlJyxcbiAgICBMSVNUX1JFQUQ6ICdyb2xlLmxpc3QucmVhZCcsXG4gICAgUEVSTUlTU0lPTl9BU1NJR046ICdyb2xlLnBlcm1pc3Npb24uYXNzaWduJyxcbiAgICBQRVJNSVNTSU9OX1JFVk9LRTogJ3JvbGUucGVybWlzc2lvbi5yZXZva2UnLFxuICAgIFBFUk1JU1NJT05fUkVBRDogJ3JvbGUucGVybWlzc2lvbi5yZWFkJyxcbiAgICBBTkFMWVRJQ1NfUkVBRDogJ3JvbGUuYW5hbHl0aWNzLnJlYWQnXG4gIH0sXG5cbiAgLy8g5qCH562+566h55CG5qih5Z2XXG4gIFRBRzoge1xuICAgIENSRUFURTogJ3RhZy50YWcuY3JlYXRlJyxcbiAgICBSRUFEOiAndGFnLnRhZy5yZWFkJyxcbiAgICBVUERBVEU6ICd0YWcudGFnLnVwZGF0ZScsXG4gICAgREVMRVRFOiAndGFnLnRhZy5kZWxldGUnLFxuICAgIExJU1RfUkVBRDogJ3RhZy5saXN0LnJlYWQnLFxuICAgIENMQVNTSUZJQ0FUSU9OX0NSRUFURTogJ3RhZy5jbGFzc2lmaWNhdGlvbi5jcmVhdGUnLFxuICAgIENMQVNTSUZJQ0FUSU9OX1JFQUQ6ICd0YWcuY2xhc3NpZmljYXRpb24ucmVhZCcsXG4gICAgQ0xBU1NJRklDQVRJT05fVVBEQVRFOiAndGFnLmNsYXNzaWZpY2F0aW9uLnVwZGF0ZScsXG4gICAgQ0xBU1NJRklDQVRJT05fREVMRVRFOiAndGFnLmNsYXNzaWZpY2F0aW9uLmRlbGV0ZScsXG4gICAgQU5BTFlUSUNTX1JFQUQ6ICd0YWcuYW5hbHl0aWNzLnJlYWQnXG4gIH0sXG5cbiAgLy8g5YiG57G7566h55CG5qih5Z2XXG4gIENMQVNTSUZJQ0FUSU9OOiB7XG4gICAgRElNRU5TSU9OX0NSRUFURTogJ2NsYXNzaWZpY2F0aW9uLmRpbWVuc2lvbi5jcmVhdGUnLFxuICAgIERJTUVOU0lPTl9SRUFEOiAnY2xhc3NpZmljYXRpb24uZGltZW5zaW9uLnJlYWQnLFxuICAgIERJTUVOU0lPTl9VUERBVEU6ICdjbGFzc2lmaWNhdGlvbi5kaW1lbnNpb24udXBkYXRlJyxcbiAgICBESU1FTlNJT05fREVMRVRFOiAnY2xhc3NpZmljYXRpb24uZGltZW5zaW9uLmRlbGV0ZScsXG4gICAgVkFMVUVfQ1JFQVRFOiAnY2xhc3NpZmljYXRpb24udmFsdWUuY3JlYXRlJyxcbiAgICBWQUxVRV9SRUFEOiAnY2xhc3NpZmljYXRpb24udmFsdWUucmVhZCcsXG4gICAgVkFMVUVfVVBEQVRFOiAnY2xhc3NpZmljYXRpb24udmFsdWUudXBkYXRlJyxcbiAgICBWQUxVRV9ERUxFVEU6ICdjbGFzc2lmaWNhdGlvbi52YWx1ZS5kZWxldGUnLFxuICAgIEFOQUxZVElDU19SRUFEOiAnY2xhc3NpZmljYXRpb24uYW5hbHl0aWNzLnJlYWQnXG4gIH0sXG5cbiAgLy8g5YiG57G7566h55CG5qih5Z2XXG4gIENMQVNTSUZJQ0FUSU9OOiB7XG4gICAgQ1JFQVRFOiAnY2xhc3NpZmljYXRpb24uY2xhc3NpZmljYXRpb24uY3JlYXRlJyxcbiAgICBSRUFEOiAnY2xhc3NpZmljYXRpb24uY2xhc3NpZmljYXRpb24ucmVhZCcsXG4gICAgVVBEQVRFOiAnY2xhc3NpZmljYXRpb24uY2xhc3NpZmljYXRpb24udXBkYXRlJyxcbiAgICBERUxFVEU6ICdjbGFzc2lmaWNhdGlvbi5jbGFzc2lmaWNhdGlvbi5kZWxldGUnLFxuICAgIExJU1RfUkVBRDogJ2NsYXNzaWZpY2F0aW9uLmxpc3QucmVhZCcsXG4gICAgRElNRU5TSU9OX0NSRUFURTogJ2NsYXNzaWZpY2F0aW9uLmRpbWVuc2lvbi5jcmVhdGUnLFxuICAgIERJTUVOU0lPTl9SRUFEOiAnY2xhc3NpZmljYXRpb24uZGltZW5zaW9uLnJlYWQnLFxuICAgIERJTUVOU0lPTl9VUERBVEU6ICdjbGFzc2lmaWNhdGlvbi5kaW1lbnNpb24udXBkYXRlJyxcbiAgICBESU1FTlNJT05fREVMRVRFOiAnY2xhc3NpZmljYXRpb24uZGltZW5zaW9uLmRlbGV0ZScsXG4gICAgVkFMVUVfQ1JFQVRFOiAnY2xhc3NpZmljYXRpb24udmFsdWUuY3JlYXRlJyxcbiAgICBWQUxVRV9SRUFEOiAnY2xhc3NpZmljYXRpb24udmFsdWUucmVhZCcsXG4gICAgVkFMVUVfVVBEQVRFOiAnY2xhc3NpZmljYXRpb24udmFsdWUudXBkYXRlJyxcbiAgICBWQUxVRV9ERUxFVEU6ICdjbGFzc2lmaWNhdGlvbi52YWx1ZS5kZWxldGUnLFxuICAgIEFOQUxZVElDU19SRUFEOiAnY2xhc3NpZmljYXRpb24uYW5hbHl0aWNzLnJlYWQnXG4gIH0sXG5cblxuICAvLyDmlbDmja7mupDnrqHnkIZcbiAgREFUQV9TT1VSQ0U6IHtcbiAgICBDUkVBVEU6ICdkYXRhX3NvdXJjZS5kYXRhX3NvdXJjZS5jcmVhdGUnLFxuICAgIFJFQUQ6ICdkYXRhX3NvdXJjZS5kYXRhX3NvdXJjZS5yZWFkJyxcbiAgICBVUERBVEU6ICdkYXRhX3NvdXJjZS5kYXRhX3NvdXJjZS51cGRhdGUnLFxuICAgIERFTEVURTogJ2RhdGFfc291cmNlLmRhdGFfc291cmNlLmRlbGV0ZScsXG4gICAgTElTVF9SRUFEOiAnZGF0YV9zb3VyY2UubGlzdC5yZWFkJyxcbiAgICBTVEFUU19SRUFEOiAnZGF0YV9zb3VyY2Uuc3RhdHMucmVhZCcsXG4gICAgQ09ORklHX0NSRUFURTogJ2RhdGFfc291cmNlLmNvbmZpZy5jcmVhdGUnLFxuICAgIENPTkZJR19SRUFEOiAnZGF0YV9zb3VyY2UuY29uZmlnLnJlYWQnLFxuICAgIENPTkZJR19MSVNUX1JFQUQ6ICdkYXRhX3NvdXJjZS5jb25maWcucmVhZCcsXG4gICAgQ09ORklHX1VQREFURTogJ2RhdGFfc291cmNlLmNvbmZpZy51cGRhdGUnLFxuICAgIENPTkZJR19ERUxFVEU6ICdkYXRhX3NvdXJjZS5jb25maWcuZGVsZXRlJyxcbiAgICBDT05GSUdfTUFOQUdFOiAnZGF0YV9zb3VyY2UuY29uZmlnLm1hbmFnZSdcbiAgfSxcblxuICAvLyDljp/lp4vmlbDmja7orrDlvZXmqKHlnZdcbiAgUkFXX0RBVEFfUkVDT1JEOiB7XG4gICAgQ1JFQVRFOiAncmF3X2RhdGFfcmVjb3JkLnJhd19kYXRhX3JlY29yZC5jcmVhdGUnLFxuICAgIFJFQUQ6ICdyYXdfZGF0YV9yZWNvcmQucmF3X2RhdGFfcmVjb3JkLnJlYWQnLFxuICAgIFVQREFURTogJ3Jhd19kYXRhX3JlY29yZC5yYXdfZGF0YV9yZWNvcmQudXBkYXRlJyxcbiAgICBERUxFVEU6ICdyYXdfZGF0YV9yZWNvcmQucmF3X2RhdGFfcmVjb3JkLmRlbGV0ZScsXG4gICAgTUFOQUdFOiAncmF3X2RhdGFfcmVjb3JkLnJhd19kYXRhX3JlY29yZC5tYW5hZ2UnLFxuICAgIEFOQUxZWkU6ICdyYXdfZGF0YV9yZWNvcmQucmF3X2RhdGFfcmVjb3JkLmFuYWx5emUnLFxuICAgIExJU1RfUkVBRDogJ3Jhd19kYXRhX3JlY29yZC5saXN0LnJlYWQnLFxuICAgIFNUQVRTX1JFQUQ6ICdyYXdfZGF0YV9yZWNvcmQuc3RhdHMucmVhZCcsXG4gICAgQVJDSElWRTogJ3Jhd19kYXRhX3JlY29yZC5yYXdfZGF0YV9yZWNvcmQuYXJjaGl2ZScsXG4gICAgQkFUQ0hfVVBEQVRFOiAncmF3X2RhdGFfcmVjb3JkLnJhd19kYXRhX3JlY29yZC5iYXRjaF91cGRhdGUnLFxuICAgIERVUExJQ0FURV9SRUFEOiAncmF3X2RhdGFfcmVjb3JkLmR1cGxpY2F0ZS5yZWFkJ1xuICB9LFxuXG4gIC8vIOaVsOaNruWkhOeQhueuoemBk+aooeWdl1xuICBEQVRBX1BST0NFU1NJTkdfUElQRUxJTkU6IHtcbiAgICBDUkVBVEU6ICdkYXRhX3Byb2Nlc3NpbmcucGlwZWxpbmUuY3JlYXRlJyxcbiAgICBSRUFEOiAnZGF0YV9wcm9jZXNzaW5nLnBpcGVsaW5lLnJlYWQnLFxuICAgIFVQREFURTogJ2RhdGFfcHJvY2Vzc2luZy5waXBlbGluZS51cGRhdGUnLFxuICAgIERFTEVURTogJ2RhdGFfcHJvY2Vzc2luZy5waXBlbGluZS5kZWxldGUnLFxuICAgIExJU1RfUkVBRDogJ2RhdGFfcHJvY2Vzc2luZy5waXBlbGluZS5yZWFkJyxcbiAgICBTVEFUU19SRUFEOiAnZGF0YV9wcm9jZXNzaW5nLnBpcGVsaW5lLnJlYWQnLFxuICAgIE1BTkFHRTogJ2RhdGFfcHJvY2Vzc2luZy5waXBlbGluZS5tYW5hZ2UnLFxuICAgIEFDVElWRV9QSVBFTElORVNfUkVBRDogJ2RhdGFfcHJvY2Vzc2luZy5waXBlbGluZS5yZWFkJ1xuICB9LFxuXG4gIC8vIOW/q+iur+euoeeQhuaooeWdl1xuICBGTEFTSF9ORVdTOiB7XG4gICAgUkVBRDogJ2ZsYXNoX25ld3MuZmxhc2hfbmV3cy5yZWFkJyxcbiAgICBVUERBVEU6ICdmbGFzaF9uZXdzLmZsYXNoX25ld3MudXBkYXRlJyxcbiAgICBERUxFVEU6ICdmbGFzaF9uZXdzLmZsYXNoX25ld3MuZGVsZXRlJyxcbiAgICBTVEFUUzogJ2ZsYXNoX25ld3MuZmxhc2hfbmV3cy5zdGF0cydcbiAgfSxcbn0gYXMgY29uc3RcblxuLy8gPT09PT09PT09PT09PT09PT09PT0g6KeS6Imy5bi46YePID09PT09PT09PT09PT09PT09PT09XG5leHBvcnQgY29uc3QgUk9MRVMgPSB7XG4gIEFETUlOOiAnYWRtaW4nLFxuICBBQ0NPVU5UX01BTkFHRVI6ICdhY2NvdW50X21hbmFnZXInLFxuICBSSVNLX09GRklDRVI6ICdyaXNrX29mZmljZXInLFxuICBVU0VSOiAndXNlcicsXG59IGFzIGNvbnN0IFxuIl0sIm5hbWVzIjpbIlJPVVRFUyIsIkxPR0lOIiwiREFTSEJPQVJEIiwiVVNFUlMiLCJVU0VSX0RFVEFJTCIsImlkIiwiVVNFUl9FRElUIiwiVVNFUl9DUkVBVEUiLCJST0xFUyIsIlJPTEVfREVUQUlMIiwiUk9MRV9FRElUIiwiUk9MRV9DUkVBVEUiLCJQRVJNSVNTSU9OUyIsIlBFUk1JU1NJT05fREVUQUlMIiwiUEVSTUlTU0lPTl9FRElUIiwiUEVSTUlTU0lPTl9DUkVBVEUiLCJERUZBVUxUX1JFRElSRUNUIiwiSE9NRSIsIkFETUlOX0xPR0lOIiwiQURNSU5fREFTSEJPQVJEIiwiQURNSU5fVVNFUlMiLCJBRE1JTl9VU0VSU19DUkVBVEUiLCJBRE1JTl9VU0VSU19FRElUIiwiQURNSU5fVVNFUlNfREVUQUlMIiwiQURNSU5fVVNFUlNfUk9MRVMiLCJBRE1JTl9ST0xFUyIsIkFETUlOX1JPTEVTX0NSRUFURSIsIkFETUlOX1JPTEVTX0VESVQiLCJBRE1JTl9ST0xFU19ERVRBSUwiLCJBRE1JTl9QRVJNSVNTSU9OUyIsIkFETUlOX1BFUk1JU1NJT05TX0NSRUFURSIsIkFETUlOX1BFUk1JU1NJT05TX0VESVQiLCJBRE1JTl9QRVJNSVNTSU9OU19ERVRBSUwiLCJBRE1JTl9ERUJVR19QRVJNSVNTSU9OUyIsIkFETUlOX0RBVEFfUFJPQ0VTU0lOR19SVUxFUyIsIkFETUlOX0RBVEFfUFJPQ0VTU0lOR19QSVBFTElORVMiLCJBRE1JTl9GTEFTSF9ORVdTIiwiQVVUSCIsIlNNU19SRVNFTkRfQ09VTlRET1dOIiwiU01TX0NPREVfTEVOR1RIIiwiVE9LRU5fU1RPUkFHRV9LRVkiLCJVU0VSX1NUT1JBR0VfS0VZIiwiUEhPTkVfUkVHRVgiLCJUT0tFTl9UWVBFIiwiVE9LRU5fRVhQSVJZIiwiQVBQIiwiTkFNRSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUFBfTkFNRSIsIkRFU0NSSVBUSU9OIiwiTkVYVF9QVUJMSUNfQVBQX0RFU0NSSVBUSU9OIiwiVkVSU0lPTiIsIk5FWFRfUFVCTElDX0FQUF9WRVJTSU9OIiwiQVBJIiwiQkFTRV9VUkwiLCJUSU1FT1VUIiwiUkVUUllfVElNRVMiLCJVSSIsIlBBR0VfU0laRV9PUFRJT05TIiwiREVGQVVMVF9QQUdFX1NJWkUiLCJUQUJMRV9TQ1JPTExfWSIsIkJSRUFLUE9JTlRTIiwieHMiLCJzbSIsIm1kIiwibGciLCJ4bCIsInh4bCIsIlVTRVJfTElTVF9SRUFEIiwiVVNFUl9QUk9GSUxFX1JFQUQiLCJVU0VSX1VQREFURSIsIlVTRVJfREVMRVRFIiwiVVNFUl9TVEFUVVNfVVBEQVRFIiwiVVNFUl9ST0xFX0FTU0lHTiIsIlVTRVJfUEVSTUlTU0lPTl9SRUFEIiwiVVNFUl9ST0xFX0JBVENIX0FTU0lHTiIsIlBFUk1JU1NJT04iLCJDUkVBVEUiLCJSRUFEIiwiVVBEQVRFIiwiREVMRVRFIiwiTElTVF9SRUFEIiwiQU5BTFlUSUNTX1JFQUQiLCJST0xFIiwiUEVSTUlTU0lPTl9BU1NJR04iLCJQRVJNSVNTSU9OX1JFVk9LRSIsIlBFUk1JU1NJT05fUkVBRCIsIlRBRyIsIkNMQVNTSUZJQ0FUSU9OX0NSRUFURSIsIkNMQVNTSUZJQ0FUSU9OX1JFQUQiLCJDTEFTU0lGSUNBVElPTl9VUERBVEUiLCJDTEFTU0lGSUNBVElPTl9ERUxFVEUiLCJDTEFTU0lGSUNBVElPTiIsIkRJTUVOU0lPTl9DUkVBVEUiLCJESU1FTlNJT05fUkVBRCIsIkRJTUVOU0lPTl9VUERBVEUiLCJESU1FTlNJT05fREVMRVRFIiwiVkFMVUVfQ1JFQVRFIiwiVkFMVUVfUkVBRCIsIlZBTFVFX1VQREFURSIsIlZBTFVFX0RFTEVURSIsIkRBVEFfU09VUkNFIiwiU1RBVFNfUkVBRCIsIkNPTkZJR19DUkVBVEUiLCJDT05GSUdfUkVBRCIsIkNPTkZJR19MSVNUX1JFQUQiLCJDT05GSUdfVVBEQVRFIiwiQ09ORklHX0RFTEVURSIsIkNPTkZJR19NQU5BR0UiLCJSQVdfREFUQV9SRUNPUkQiLCJNQU5BR0UiLCJBTkFMWVpFIiwiQVJDSElWRSIsIkJBVENIX1VQREFURSIsIkRVUExJQ0FURV9SRUFEIiwiREFUQV9QUk9DRVNTSU5HX1BJUEVMSU5FIiwiQUNUSVZFX1BJUEVMSU5FU19SRUFEIiwiRkxBU0hfTkVXUyIsIlNUQVRTIiwiQURNSU4iLCJBQ0NPVU5UX01BTkFHRVIiLCJSSVNLX09GRklDRVIiLCJVU0VSIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/constants/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/contexts/PermissionContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/PermissionContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PermissionProvider: () => (/* binding */ PermissionProvider),\n/* harmony export */   usePermissions: () => (/* binding */ usePermissions),\n/* harmony export */   useUserPermissions: () => (/* binding */ useUserPermissions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_userAPI__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/userAPI */ \"(ssr)/./src/services/userAPI.ts\");\n/**\n * 权限管理Context\n * 提供权限检查、角色验证等功能\n */ /* __next_internal_client_entry_do_not_use__ PermissionProvider,usePermissions,useUserPermissions auto */ \n\n\n\n/**\n * 权限上下文\n */ const PermissionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    permissions: [],\n    roles: [],\n    loading: true,\n    error: null,\n    hasPermission: ()=>false,\n    hasRole: ()=>false,\n    hasAnyPermission: ()=>false,\n    hasAllPermissions: ()=>false,\n    refreshPermissions: ()=>{}\n});\n/**\n * 不需要权限检查的公开路由\n */ const PUBLIC_ROUTES = [\n    \"/admin/login\",\n    \"/\",\n    \"/login\",\n    \"/register\",\n    \"/403\",\n    \"/404\",\n    \"/500\"\n];\n/**\n * 权限提供者组件\n */ const PermissionProvider = ({ children })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 确保组件在客户端环境\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    /**\n   * 检查当前路由是否为公开路由\n   */ const isPublicRoute = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((currentPath)=>{\n        return PUBLIC_ROUTES.some((route)=>{\n            if (route === currentPath) return true;\n            // 支持通配符匹配，例如 /api/* \n            if (route.endsWith(\"*\")) {\n                return currentPath.startsWith(route.slice(0, -1));\n            }\n            return false;\n        });\n    }, []);\n    /**\n   * 从localStorage恢复权限数据\n   */ const loadPermissionsFromStorage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // 仅在客户端环境执行\n        if (!isClient || \"undefined\" === \"undefined\") return false;\n        try {\n            const userStr = localStorage.getItem(\"user\");\n            if (userStr) {\n                const user = JSON.parse(userStr);\n                if (user.permission_codes && user.roles) {\n                    setPermissions(user.permission_codes);\n                    setRoles(user.roles);\n                    return true;\n                } else {\n                    console.warn(\"⚠️ 用户数据中缺少权限信息\");\n                }\n            }\n        } catch (error) {\n            console.warn(\"❌ 从localStorage恢复权限失败:\", error);\n        }\n        return false;\n    }, [\n        isClient\n    ]);\n    /**\n   * 安全的重定向到登录页面\n   */ const redirectToLogin = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // 仅在客户端环境执行重定向\n        if (!isClient || \"undefined\" === \"undefined\") return;\n        try {\n            // 使用 Next.js 路由进行重定向\n            router.push(\"/admin/login\");\n        } catch (error) {\n            console.error(\"重定向到登录页失败:\", error);\n            // 备用方案：使用 window.location\n            if (false) {}\n        }\n    }, [\n        router,\n        isClient\n    ]);\n    /**\n   * 获取用户权限（仅在客户端执行）\n   */ const fetchUserPermissions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // 仅在客户端环境执行\n        if (!isClient) {\n            return;\n        }\n        // 如果是公开路由，跳过权限检查\n        if (isPublicRoute(pathname)) {\n            console.log(\"\\uD83D\\uDD13 公开路由，跳过权限检查:\", pathname);\n            setLoading(false);\n            return;\n        }\n        if (!_services_userAPI__WEBPACK_IMPORTED_MODULE_3__.userAPI.isAuthenticated()) {\n            console.log(\"\\uD83D\\uDD12 用户未认证，重定向到登录页面\");\n            setLoading(false);\n            redirectToLogin();\n            return;\n        }\n        // 尝试从localStorage恢复权限\n        if (loadPermissionsFromStorage()) {\n            setLoading(false);\n            return;\n        } else {\n            // 本地无数据，说明不是登录状态，跳转到登录\n            console.log(\"\\uD83D\\uDD12 本地无权限数据，重定向到登录页面\");\n            redirectToLogin();\n        }\n    }, [\n        loadPermissionsFromStorage,\n        pathname,\n        isPublicRoute,\n        redirectToLogin,\n        isClient\n    ]);\n    /**\n   * 检查是否具有指定权限\n   */ const hasPermission = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((permission)=>{\n        // 直接检查权限\n        return permissions.includes(permission);\n    }, [\n        permissions\n    ]);\n    /**\n   * 检查是否具有指定角色\n   */ const hasRole = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((roleName)=>{\n        return roles.some((role)=>role.name === roleName);\n    }, [\n        roles\n    ]);\n    /**\n   * 检查是否具有任意一个权限\n   */ const hasAnyPermission = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((permissionList)=>{\n        return permissionList.some((permission)=>permissions.includes(permission));\n    }, [\n        permissions\n    ]);\n    /**\n   * 检查是否具有所有权限\n   */ const hasAllPermissions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((permissionList)=>{\n        return permissionList.every((permission)=>permissions.includes(permission));\n    }, [\n        permissions\n    ]);\n    /**\n   * 刷新权限\n   */ const refreshPermissions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        fetchUserPermissions();\n    }, [\n        fetchUserPermissions\n    ]);\n    // 组件挂载时和路由变化时获取权限\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchUserPermissions();\n    }, [\n        fetchUserPermissions\n    ]);\n    const contextValue = {\n        permissions,\n        roles,\n        loading,\n        error,\n        hasPermission,\n        hasRole,\n        hasAnyPermission,\n        hasAllPermissions,\n        refreshPermissions\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PermissionContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/contexts/PermissionContext.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, undefined);\n};\n/**\n * 权限Context Hook\n */ const usePermissions = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PermissionContext);\n    if (!context) {\n        throw new Error(\"usePermissions must be used within a PermissionProvider\");\n    }\n    return context;\n};\n/**\n * 用户权限Hook（向后兼容）\n */ const useUserPermissions = ()=>{\n    const context = usePermissions();\n    return {\n        permissions: context.permissions,\n        roles: context.roles,\n        loading: context.loading,\n        error: context.error,\n        hasPermission: context.hasPermission,\n        hasRole: context.hasRole,\n        hasAnyPermission: context.hasAnyPermission,\n        hasAllPermissions: context.hasAllPermissions,\n        refreshPermissions: context.refreshPermissions\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvUGVybWlzc2lvbkNvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBOzs7Q0FHQztBQUl5RjtBQUNsQztBQUNaO0FBa0I1Qzs7Q0FFQyxHQUNELE1BQU1TLGtDQUFvQlIsb0RBQWFBLENBQXdCO0lBQzdEUyxhQUFhLEVBQUU7SUFDZkMsT0FBTyxFQUFFO0lBQ1RDLFNBQVM7SUFDVEMsT0FBTztJQUNQQyxlQUFlLElBQU07SUFDckJDLFNBQVMsSUFBTTtJQUNmQyxrQkFBa0IsSUFBTTtJQUN4QkMsbUJBQW1CLElBQU07SUFDekJDLG9CQUFvQixLQUFPO0FBQzdCO0FBU0E7O0NBRUMsR0FDRCxNQUFNQyxnQkFBZ0I7SUFDcEI7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDRDtBQUVEOztDQUVDLEdBQ00sTUFBTUMscUJBQXdELENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQ2hGLE1BQU1DLFdBQVdoQiw0REFBV0E7SUFDNUIsTUFBTWlCLFNBQVNoQiwwREFBU0E7SUFDeEIsTUFBTSxDQUFDRyxhQUFhYyxlQUFlLEdBQUdyQiwrQ0FBUUEsQ0FBVyxFQUFFO0lBQzNELE1BQU0sQ0FBQ1EsT0FBT2MsU0FBUyxHQUFHdEIsK0NBQVFBLENBQWEsRUFBRTtJQUNqRCxNQUFNLENBQUNTLFNBQVNjLFdBQVcsR0FBR3ZCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ1UsTUFBTSxHQUFHViwrQ0FBUUEsQ0FBZ0I7SUFDeEMsTUFBTSxDQUFDd0IsVUFBVUMsWUFBWSxHQUFHekIsK0NBQVFBLENBQUM7SUFFekMsYUFBYTtJQUNiQyxnREFBU0EsQ0FBQztRQUNSd0IsWUFBWTtJQUNkLEdBQUcsRUFBRTtJQUVMOztHQUVDLEdBQ0QsTUFBTUMsZ0JBQWdCeEIsa0RBQVdBLENBQUMsQ0FBQ3lCO1FBQ2pDLE9BQU9YLGNBQWNZLElBQUksQ0FBQ0MsQ0FBQUE7WUFDeEIsSUFBSUEsVUFBVUYsYUFBYSxPQUFPO1lBQ2xDLHFCQUFxQjtZQUNyQixJQUFJRSxNQUFNQyxRQUFRLENBQUMsTUFBTTtnQkFDdkIsT0FBT0gsWUFBWUksVUFBVSxDQUFDRixNQUFNRyxLQUFLLENBQUMsR0FBRyxDQUFDO1lBQ2hEO1lBQ0EsT0FBTztRQUNUO0lBQ0YsR0FBRyxFQUFFO0lBRUw7O0dBRUMsR0FDRCxNQUFNQyw2QkFBNkIvQixrREFBV0EsQ0FBQztRQUM3QyxZQUFZO1FBQ1osSUFBSSxDQUFDc0IsWUFBWSxnQkFBa0IsYUFBYSxPQUFPO1FBRXZELElBQUk7WUFDRixNQUFNVSxVQUFVQyxhQUFhQyxPQUFPLENBQUM7WUFFckMsSUFBSUYsU0FBUztnQkFDWCxNQUFNRyxPQUFPQyxLQUFLQyxLQUFLLENBQUNMO2dCQUV4QixJQUFJRyxLQUFLRyxnQkFBZ0IsSUFBSUgsS0FBSzdCLEtBQUssRUFBRTtvQkFDdkNhLGVBQWVnQixLQUFLRyxnQkFBZ0I7b0JBQ3BDbEIsU0FBU2UsS0FBSzdCLEtBQUs7b0JBQ25CLE9BQU87Z0JBQ1QsT0FBTztvQkFDTGlDLFFBQVFDLElBQUksQ0FBQztnQkFDZjtZQUNGO1FBQ0YsRUFBRSxPQUFPaEMsT0FBTztZQUNkK0IsUUFBUUMsSUFBSSxDQUFDLDBCQUEwQmhDO1FBQ3pDO1FBQ0EsT0FBTztJQUNULEdBQUc7UUFBQ2M7S0FBUztJQUViOztHQUVDLEdBQ0QsTUFBTW1CLGtCQUFrQnpDLGtEQUFXQSxDQUFDO1FBQ2xDLGVBQWU7UUFDZixJQUFJLENBQUNzQixZQUFZLGdCQUFrQixhQUFhO1FBRWhELElBQUk7WUFDRixxQkFBcUI7WUFDckJKLE9BQU93QixJQUFJLENBQUM7UUFDZCxFQUFFLE9BQU9sQyxPQUFPO1lBQ2QrQixRQUFRL0IsS0FBSyxDQUFDLGNBQWNBO1lBQzVCLDBCQUEwQjtZQUMxQixJQUFJLEtBQWtCLEVBQWEsRUFFbEM7UUFDSDtJQUNGLEdBQUc7UUFBQ1U7UUFBUUk7S0FBUztJQUVyQjs7R0FFQyxHQUNELE1BQU13Qix1QkFBdUI5QyxrREFBV0EsQ0FBQztRQUN2QyxZQUFZO1FBQ1osSUFBSSxDQUFDc0IsVUFBVTtZQUNiO1FBQ0Y7UUFFQSxpQkFBaUI7UUFDakIsSUFBSUUsY0FBY1AsV0FBVztZQUMzQnNCLFFBQVFRLEdBQUcsQ0FBQyw2QkFBbUI5QjtZQUMvQkksV0FBVztZQUNYO1FBQ0Y7UUFFQSxJQUFJLENBQUNsQixzREFBT0EsQ0FBQzZDLGVBQWUsSUFBSTtZQUM5QlQsUUFBUVEsR0FBRyxDQUFDO1lBQ1oxQixXQUFXO1lBQ1hvQjtZQUNBO1FBQ0Y7UUFFQSxzQkFBc0I7UUFDdEIsSUFBSVYsOEJBQThCO1lBQ2hDVixXQUFXO1lBQ1g7UUFDRixPQUFPO1lBQ0wsdUJBQXVCO1lBQ3ZCa0IsUUFBUVEsR0FBRyxDQUFDO1lBQ1pOO1FBQ0Y7SUFDRixHQUFHO1FBQUNWO1FBQTRCZDtRQUFVTztRQUFlaUI7UUFBaUJuQjtLQUFTO0lBRW5GOztHQUVDLEdBQ0QsTUFBTWIsZ0JBQWdCVCxrREFBV0EsQ0FBQyxDQUFDaUQ7UUFDakMsU0FBUztRQUNULE9BQU81QyxZQUFZNkMsUUFBUSxDQUFDRDtJQUM5QixHQUFHO1FBQUM1QztLQUFZO0lBRWhCOztHQUVDLEdBQ0QsTUFBTUssVUFBVVYsa0RBQVdBLENBQUMsQ0FBQ21EO1FBQzNCLE9BQU83QyxNQUFNb0IsSUFBSSxDQUFDMEIsQ0FBQUEsT0FBUUEsS0FBS0MsSUFBSSxLQUFLRjtJQUMxQyxHQUFHO1FBQUM3QztLQUFNO0lBRVY7O0dBRUMsR0FDRCxNQUFNSyxtQkFBbUJYLGtEQUFXQSxDQUFDLENBQUNzRDtRQUNwQyxPQUFPQSxlQUFlNUIsSUFBSSxDQUFDdUIsQ0FBQUEsYUFBYzVDLFlBQVk2QyxRQUFRLENBQUNEO0lBQ2hFLEdBQUc7UUFBQzVDO0tBQVk7SUFFaEI7O0dBRUMsR0FDRCxNQUFNTyxvQkFBb0JaLGtEQUFXQSxDQUFDLENBQUNzRDtRQUNyQyxPQUFPQSxlQUFlQyxLQUFLLENBQUNOLENBQUFBLGFBQWM1QyxZQUFZNkMsUUFBUSxDQUFDRDtJQUNqRSxHQUFHO1FBQUM1QztLQUFZO0lBRWhCOztHQUVDLEdBQ0QsTUFBTVEscUJBQXFCYixrREFBV0EsQ0FBQztRQUNyQzhDO0lBQ0YsR0FBRztRQUFDQTtLQUFxQjtJQUV6QixrQkFBa0I7SUFDbEIvQyxnREFBU0EsQ0FBQztRQUNSK0M7SUFDRixHQUFHO1FBQUNBO0tBQXFCO0lBRXpCLE1BQU1VLGVBQXNDO1FBQzFDbkQ7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7SUFDRjtJQUVBLHFCQUNFLDhEQUFDVCxrQkFBa0JxRCxRQUFRO1FBQUNDLE9BQU9GO2tCQUNoQ3hDOzs7Ozs7QUFHUCxFQUFDO0FBRUQ7O0NBRUMsR0FDTSxNQUFNMkMsaUJBQWlCO0lBQzVCLE1BQU1DLFVBQVUvRCxpREFBVUEsQ0FBQ087SUFDM0IsSUFBSSxDQUFDd0QsU0FBUztRQUNaLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBQztBQUVEOztDQUVDLEdBQ00sTUFBTUUscUJBQXFCO0lBQ2hDLE1BQU1GLFVBQVVEO0lBQ2hCLE9BQU87UUFDTHRELGFBQWF1RCxRQUFRdkQsV0FBVztRQUNoQ0MsT0FBT3NELFFBQVF0RCxLQUFLO1FBQ3BCQyxTQUFTcUQsUUFBUXJELE9BQU87UUFDeEJDLE9BQU9vRCxRQUFRcEQsS0FBSztRQUNwQkMsZUFBZW1ELFFBQVFuRCxhQUFhO1FBQ3BDQyxTQUFTa0QsUUFBUWxELE9BQU87UUFDeEJDLGtCQUFrQmlELFFBQVFqRCxnQkFBZ0I7UUFDMUNDLG1CQUFtQmdELFFBQVFoRCxpQkFBaUI7UUFDNUNDLG9CQUFvQitDLFFBQVEvQyxrQkFBa0I7SUFDaEQ7QUFDRixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmluc2lnaHQtY29uc29sZS8uL3NyYy9jb250ZXh0cy9QZXJtaXNzaW9uQ29udGV4dC50c3g/ZThjYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIOadg+mZkOeuoeeQhkNvbnRleHRcbiAqIOaPkOS+m+adg+mZkOajgOafpeOAgeinkuiJsumqjOivgeetieWKn+iDvVxuICovXG5cbid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QsIHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZVBhdGhuYW1lLCB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgeyB1c2VyQVBJIH0gZnJvbSAnQC9zZXJ2aWNlcy91c2VyQVBJJ1xuaW1wb3J0IHsgUm9sZUluZm8sIEFkbWluVXNlclJlc3BvbnNlIH0gZnJvbSAnQC90eXBlcy9hcGknXG5cbi8qKlxuICog5p2D6ZmQ5LiK5LiL5paH57G75Z6L5a6a5LmJXG4gKi9cbmludGVyZmFjZSBQZXJtaXNzaW9uQ29udGV4dFR5cGUge1xuICBwZXJtaXNzaW9uczogc3RyaW5nW11cbiAgcm9sZXM6IFJvbGVJbmZvW11cbiAgbG9hZGluZzogYm9vbGVhblxuICBlcnJvcjogc3RyaW5nIHwgbnVsbFxuICBoYXNQZXJtaXNzaW9uOiAocGVybWlzc2lvbjogc3RyaW5nKSA9PiBib29sZWFuXG4gIGhhc1JvbGU6IChyb2xlTmFtZTogc3RyaW5nKSA9PiBib29sZWFuXG4gIGhhc0FueVBlcm1pc3Npb246IChwZXJtaXNzaW9uczogc3RyaW5nW10pID0+IGJvb2xlYW5cbiAgaGFzQWxsUGVybWlzc2lvbnM6IChwZXJtaXNzaW9uczogc3RyaW5nW10pID0+IGJvb2xlYW5cbiAgcmVmcmVzaFBlcm1pc3Npb25zOiAoKSA9PiB2b2lkXG59XG5cbi8qKlxuICog5p2D6ZmQ5LiK5LiL5paHXG4gKi9cbmNvbnN0IFBlcm1pc3Npb25Db250ZXh0ID0gY3JlYXRlQ29udGV4dDxQZXJtaXNzaW9uQ29udGV4dFR5cGU+KHtcbiAgcGVybWlzc2lvbnM6IFtdLFxuICByb2xlczogW10sXG4gIGxvYWRpbmc6IHRydWUsXG4gIGVycm9yOiBudWxsLFxuICBoYXNQZXJtaXNzaW9uOiAoKSA9PiBmYWxzZSxcbiAgaGFzUm9sZTogKCkgPT4gZmFsc2UsXG4gIGhhc0FueVBlcm1pc3Npb246ICgpID0+IGZhbHNlLFxuICBoYXNBbGxQZXJtaXNzaW9uczogKCkgPT4gZmFsc2UsXG4gIHJlZnJlc2hQZXJtaXNzaW9uczogKCkgPT4ge30sXG59KVxuXG4vKipcbiAqIOadg+mZkOaPkOS+m+iAhee7hOS7tuWxnuaAp1xuICovXG5pbnRlcmZhY2UgUGVybWlzc2lvblByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59XG5cbi8qKlxuICog5LiN6ZyA6KaB5p2D6ZmQ5qOA5p+l55qE5YWs5byA6Lev55SxXG4gKi9cbmNvbnN0IFBVQkxJQ19ST1VURVMgPSBbXG4gICcvYWRtaW4vbG9naW4nLFxuICAnLycsXG4gICcvbG9naW4nLFxuICAnL3JlZ2lzdGVyJyxcbiAgJy80MDMnLFxuICAnLzQwNCcsXG4gICcvNTAwJ1xuXVxuXG4vKipcbiAqIOadg+mZkOaPkOS+m+iAhee7hOS7tlxuICovXG5leHBvcnQgY29uc3QgUGVybWlzc2lvblByb3ZpZGVyOiBSZWFjdC5GQzxQZXJtaXNzaW9uUHJvdmlkZXJQcm9wcz4gPSAoeyBjaGlsZHJlbiB9KSA9PiB7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKVxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICBjb25zdCBbcGVybWlzc2lvbnMsIHNldFBlcm1pc3Npb25zXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSlcbiAgY29uc3QgW3JvbGVzLCBzZXRSb2xlc10gPSB1c2VTdGF0ZTxSb2xlSW5mb1tdPihbXSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2Vycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtpc0NsaWVudCwgc2V0SXNDbGllbnRdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgLy8g56Gu5L+d57uE5Lu25Zyo5a6i5oi356uv546v5aKDXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0SXNDbGllbnQodHJ1ZSlcbiAgfSwgW10pXG5cbiAgLyoqXG4gICAqIOajgOafpeW9k+WJjei3r+eUseaYr+WQpuS4uuWFrOW8gOi3r+eUsVxuICAgKi9cbiAgY29uc3QgaXNQdWJsaWNSb3V0ZSA9IHVzZUNhbGxiYWNrKChjdXJyZW50UGF0aDogc3RyaW5nKTogYm9vbGVhbiA9PiB7XG4gICAgcmV0dXJuIFBVQkxJQ19ST1VURVMuc29tZShyb3V0ZSA9PiB7XG4gICAgICBpZiAocm91dGUgPT09IGN1cnJlbnRQYXRoKSByZXR1cm4gdHJ1ZVxuICAgICAgLy8g5pSv5oyB6YCa6YWN56ym5Yy56YWN77yM5L6L5aaCIC9hcGkvKiBcbiAgICAgIGlmIChyb3V0ZS5lbmRzV2l0aCgnKicpKSB7XG4gICAgICAgIHJldHVybiBjdXJyZW50UGF0aC5zdGFydHNXaXRoKHJvdXRlLnNsaWNlKDAsIC0xKSlcbiAgICAgIH1cbiAgICAgIHJldHVybiBmYWxzZVxuICAgIH0pXG4gIH0sIFtdKVxuXG4gIC8qKlxuICAgKiDku45sb2NhbFN0b3JhZ2XmgaLlpI3mnYPpmZDmlbDmja5cbiAgICovXG4gIGNvbnN0IGxvYWRQZXJtaXNzaW9uc0Zyb21TdG9yYWdlID0gdXNlQ2FsbGJhY2soKCk6IGJvb2xlYW4gPT4ge1xuICAgIC8vIOS7heWcqOWuouaIt+err+eOr+Wig+aJp+ihjFxuICAgIGlmICghaXNDbGllbnQgfHwgdHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBmYWxzZVxuICAgIFxuICAgIHRyeSB7XG4gICAgICBjb25zdCB1c2VyU3RyID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZXInKVxuICAgICAgXG4gICAgICBpZiAodXNlclN0cikge1xuICAgICAgICBjb25zdCB1c2VyID0gSlNPTi5wYXJzZSh1c2VyU3RyKSBhcyBBZG1pblVzZXJSZXNwb25zZSAmIHsgcGVybWlzc2lvbl9jb2Rlcz86IHN0cmluZ1tdIH1cbiAgICAgICAgXG4gICAgICAgIGlmICh1c2VyLnBlcm1pc3Npb25fY29kZXMgJiYgdXNlci5yb2xlcykge1xuICAgICAgICAgIHNldFBlcm1pc3Npb25zKHVzZXIucGVybWlzc2lvbl9jb2RlcylcbiAgICAgICAgICBzZXRSb2xlcyh1c2VyLnJvbGVzKVxuICAgICAgICAgIHJldHVybiB0cnVlXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8g55So5oi35pWw5o2u5Lit57y65bCR5p2D6ZmQ5L+h5oGvJylcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ+KdjCDku45sb2NhbFN0b3JhZ2XmgaLlpI3mnYPpmZDlpLHotKU6JywgZXJyb3IpXG4gICAgfVxuICAgIHJldHVybiBmYWxzZVxuICB9LCBbaXNDbGllbnRdKVxuXG4gIC8qKlxuICAgKiDlronlhajnmoTph43lrprlkJHliLDnmbvlvZXpobXpnaJcbiAgICovXG4gIGNvbnN0IHJlZGlyZWN0VG9Mb2dpbiA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICAvLyDku4XlnKjlrqLmiLfnq6/njq/looPmiafooYzph43lrprlkJFcbiAgICBpZiAoIWlzQ2xpZW50IHx8IHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm5cbiAgICBcbiAgICB0cnkge1xuICAgICAgLy8g5L2/55SoIE5leHQuanMg6Lev55Sx6L+b6KGM6YeN5a6a5ZCRXG4gICAgICByb3V0ZXIucHVzaCgnL2FkbWluL2xvZ2luJylcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign6YeN5a6a5ZCR5Yiw55m75b2V6aG15aSx6LSlOicsIGVycm9yKVxuICAgICAgLy8g5aSH55So5pa55qGI77ya5L2/55SoIHdpbmRvdy5sb2NhdGlvblxuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9hZG1pbi9sb2dpbidcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtyb3V0ZXIsIGlzQ2xpZW50XSlcblxuICAvKipcbiAgICog6I635Y+W55So5oi35p2D6ZmQ77yI5LuF5Zyo5a6i5oi356uv5omn6KGM77yJXG4gICAqL1xuICBjb25zdCBmZXRjaFVzZXJQZXJtaXNzaW9ucyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICAvLyDku4XlnKjlrqLmiLfnq6/njq/looPmiafooYxcbiAgICBpZiAoIWlzQ2xpZW50KSB7XG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICAvLyDlpoLmnpzmmK/lhazlvIDot6/nlLHvvIzot7Pov4fmnYPpmZDmo4Dmn6VcbiAgICBpZiAoaXNQdWJsaWNSb3V0ZShwYXRobmFtZSkpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5STIOWFrOW8gOi3r+eUse+8jOi3s+i/h+adg+mZkOajgOafpTonLCBwYXRobmFtZSlcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICBpZiAoIXVzZXJBUEkuaXNBdXRoZW50aWNhdGVkKCkpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SSIOeUqOaIt+acquiupOivge+8jOmHjeWumuWQkeWIsOeZu+W9lemhtemdoicpXG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgICAgcmVkaXJlY3RUb0xvZ2luKClcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIC8vIOWwneivleS7jmxvY2FsU3RvcmFnZeaBouWkjeadg+mZkFxuICAgIGlmIChsb2FkUGVybWlzc2lvbnNGcm9tU3RvcmFnZSgpKSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgICAgcmV0dXJuXG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIOacrOWcsOaXoOaVsOaNru+8jOivtOaYjuS4jeaYr+eZu+W9leeKtuaAge+8jOi3s+i9rOWIsOeZu+W9lVxuICAgICAgY29uc29sZS5sb2coJ/CflJIg5pys5Zyw5peg5p2D6ZmQ5pWw5o2u77yM6YeN5a6a5ZCR5Yiw55m75b2V6aG16Z2iJylcbiAgICAgIHJlZGlyZWN0VG9Mb2dpbigpXG4gICAgfVxuICB9LCBbbG9hZFBlcm1pc3Npb25zRnJvbVN0b3JhZ2UsIHBhdGhuYW1lLCBpc1B1YmxpY1JvdXRlLCByZWRpcmVjdFRvTG9naW4sIGlzQ2xpZW50XSlcblxuICAvKipcbiAgICog5qOA5p+l5piv5ZCm5YW35pyJ5oyH5a6a5p2D6ZmQXG4gICAqL1xuICBjb25zdCBoYXNQZXJtaXNzaW9uID0gdXNlQ2FsbGJhY2soKHBlcm1pc3Npb246IHN0cmluZyk6IGJvb2xlYW4gPT4ge1xuICAgIC8vIOebtOaOpeajgOafpeadg+mZkFxuICAgIHJldHVybiBwZXJtaXNzaW9ucy5pbmNsdWRlcyhwZXJtaXNzaW9uKVxuICB9LCBbcGVybWlzc2lvbnNdKVxuXG4gIC8qKlxuICAgKiDmo4Dmn6XmmK/lkKblhbfmnInmjIflrprop5LoibJcbiAgICovXG4gIGNvbnN0IGhhc1JvbGUgPSB1c2VDYWxsYmFjaygocm9sZU5hbWU6IHN0cmluZyk6IGJvb2xlYW4gPT4ge1xuICAgIHJldHVybiByb2xlcy5zb21lKHJvbGUgPT4gcm9sZS5uYW1lID09PSByb2xlTmFtZSlcbiAgfSwgW3JvbGVzXSlcblxuICAvKipcbiAgICog5qOA5p+l5piv5ZCm5YW35pyJ5Lu75oSP5LiA5Liq5p2D6ZmQXG4gICAqL1xuICBjb25zdCBoYXNBbnlQZXJtaXNzaW9uID0gdXNlQ2FsbGJhY2soKHBlcm1pc3Npb25MaXN0OiBzdHJpbmdbXSk6IGJvb2xlYW4gPT4ge1xuICAgIHJldHVybiBwZXJtaXNzaW9uTGlzdC5zb21lKHBlcm1pc3Npb24gPT4gcGVybWlzc2lvbnMuaW5jbHVkZXMocGVybWlzc2lvbikpXG4gIH0sIFtwZXJtaXNzaW9uc10pXG5cbiAgLyoqXG4gICAqIOajgOafpeaYr+WQpuWFt+acieaJgOacieadg+mZkFxuICAgKi9cbiAgY29uc3QgaGFzQWxsUGVybWlzc2lvbnMgPSB1c2VDYWxsYmFjaygocGVybWlzc2lvbkxpc3Q6IHN0cmluZ1tdKTogYm9vbGVhbiA9PiB7XG4gICAgcmV0dXJuIHBlcm1pc3Npb25MaXN0LmV2ZXJ5KHBlcm1pc3Npb24gPT4gcGVybWlzc2lvbnMuaW5jbHVkZXMocGVybWlzc2lvbikpXG4gIH0sIFtwZXJtaXNzaW9uc10pXG5cbiAgLyoqXG4gICAqIOWIt+aWsOadg+mZkFxuICAgKi9cbiAgY29uc3QgcmVmcmVzaFBlcm1pc3Npb25zID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGZldGNoVXNlclBlcm1pc3Npb25zKClcbiAgfSwgW2ZldGNoVXNlclBlcm1pc3Npb25zXSlcblxuICAvLyDnu4Tku7bmjILovb3ml7blkozot6/nlLHlj5jljJbml7bojrflj5bmnYPpmZBcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaFVzZXJQZXJtaXNzaW9ucygpXG4gIH0sIFtmZXRjaFVzZXJQZXJtaXNzaW9uc10pXG5cbiAgY29uc3QgY29udGV4dFZhbHVlOiBQZXJtaXNzaW9uQ29udGV4dFR5cGUgPSB7XG4gICAgcGVybWlzc2lvbnMsXG4gICAgcm9sZXMsXG4gICAgbG9hZGluZyxcbiAgICBlcnJvcixcbiAgICBoYXNQZXJtaXNzaW9uLFxuICAgIGhhc1JvbGUsXG4gICAgaGFzQW55UGVybWlzc2lvbixcbiAgICBoYXNBbGxQZXJtaXNzaW9ucyxcbiAgICByZWZyZXNoUGVybWlzc2lvbnMsXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxQZXJtaXNzaW9uQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17Y29udGV4dFZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1Blcm1pc3Npb25Db250ZXh0LlByb3ZpZGVyPlxuICApXG59XG5cbi8qKlxuICog5p2D6ZmQQ29udGV4dCBIb29rXG4gKi9cbmV4cG9ydCBjb25zdCB1c2VQZXJtaXNzaW9ucyA9ICgpOiBQZXJtaXNzaW9uQ29udGV4dFR5cGUgPT4ge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChQZXJtaXNzaW9uQ29udGV4dClcbiAgaWYgKCFjb250ZXh0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VQZXJtaXNzaW9ucyBtdXN0IGJlIHVzZWQgd2l0aGluIGEgUGVybWlzc2lvblByb3ZpZGVyJylcbiAgfVxuICByZXR1cm4gY29udGV4dFxufVxuXG4vKipcbiAqIOeUqOaIt+adg+mZkEhvb2vvvIjlkJHlkI7lhbzlrrnvvIlcbiAqL1xuZXhwb3J0IGNvbnN0IHVzZVVzZXJQZXJtaXNzaW9ucyA9ICgpID0+IHtcbiAgY29uc3QgY29udGV4dCA9IHVzZVBlcm1pc3Npb25zKClcbiAgcmV0dXJuIHtcbiAgICBwZXJtaXNzaW9uczogY29udGV4dC5wZXJtaXNzaW9ucyxcbiAgICByb2xlczogY29udGV4dC5yb2xlcyxcbiAgICBsb2FkaW5nOiBjb250ZXh0LmxvYWRpbmcsXG4gICAgZXJyb3I6IGNvbnRleHQuZXJyb3IsXG4gICAgaGFzUGVybWlzc2lvbjogY29udGV4dC5oYXNQZXJtaXNzaW9uLFxuICAgIGhhc1JvbGU6IGNvbnRleHQuaGFzUm9sZSxcbiAgICBoYXNBbnlQZXJtaXNzaW9uOiBjb250ZXh0Lmhhc0FueVBlcm1pc3Npb24sXG4gICAgaGFzQWxsUGVybWlzc2lvbnM6IGNvbnRleHQuaGFzQWxsUGVybWlzc2lvbnMsXG4gICAgcmVmcmVzaFBlcm1pc3Npb25zOiBjb250ZXh0LnJlZnJlc2hQZXJtaXNzaW9ucyxcbiAgfVxufSAiXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJ1c2VQYXRobmFtZSIsInVzZVJvdXRlciIsInVzZXJBUEkiLCJQZXJtaXNzaW9uQ29udGV4dCIsInBlcm1pc3Npb25zIiwicm9sZXMiLCJsb2FkaW5nIiwiZXJyb3IiLCJoYXNQZXJtaXNzaW9uIiwiaGFzUm9sZSIsImhhc0FueVBlcm1pc3Npb24iLCJoYXNBbGxQZXJtaXNzaW9ucyIsInJlZnJlc2hQZXJtaXNzaW9ucyIsIlBVQkxJQ19ST1VURVMiLCJQZXJtaXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiIsInBhdGhuYW1lIiwicm91dGVyIiwic2V0UGVybWlzc2lvbnMiLCJzZXRSb2xlcyIsInNldExvYWRpbmciLCJpc0NsaWVudCIsInNldElzQ2xpZW50IiwiaXNQdWJsaWNSb3V0ZSIsImN1cnJlbnRQYXRoIiwic29tZSIsInJvdXRlIiwiZW5kc1dpdGgiLCJzdGFydHNXaXRoIiwic2xpY2UiLCJsb2FkUGVybWlzc2lvbnNGcm9tU3RvcmFnZSIsInVzZXJTdHIiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwidXNlciIsIkpTT04iLCJwYXJzZSIsInBlcm1pc3Npb25fY29kZXMiLCJjb25zb2xlIiwid2FybiIsInJlZGlyZWN0VG9Mb2dpbiIsInB1c2giLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJmZXRjaFVzZXJQZXJtaXNzaW9ucyIsImxvZyIsImlzQXV0aGVudGljYXRlZCIsInBlcm1pc3Npb24iLCJpbmNsdWRlcyIsInJvbGVOYW1lIiwicm9sZSIsIm5hbWUiLCJwZXJtaXNzaW9uTGlzdCIsImV2ZXJ5IiwiY29udGV4dFZhbHVlIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZVBlcm1pc3Npb25zIiwiY29udGV4dCIsIkVycm9yIiwidXNlVXNlclBlcm1pc3Npb25zIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/PermissionContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/redux.ts":
/*!****************************!*\
  !*** ./src/hooks/redux.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppDispatch: () => (/* binding */ useAppDispatch),\n/* harmony export */   useAppSelector: () => (/* binding */ useAppSelector)\n/* harmony export */ });\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/es/index.js\");\n/**\n * Redux hooks\n * 提供类型安全的dispatch和selector\n */ \n/**\n * 类型安全的useDispatch hook\n */ const useAppDispatch = ()=>(0,react_redux__WEBPACK_IMPORTED_MODULE_0__.useDispatch)();\n/**\n * 类型安全的useSelector hook\n */ const useAppSelector = react_redux__WEBPACK_IMPORTED_MODULE_0__.useSelector;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvcmVkdXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7OztDQUdDLEdBRTJFO0FBRzVFOztDQUVDLEdBQ00sTUFBTUUsaUJBQWlCLElBQU1GLHdEQUFXQSxHQUFlO0FBRTlEOztDQUVDLEdBQ00sTUFBTUcsaUJBQWtERixvREFBV0EsQ0FBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ZpbnNpZ2h0LWNvbnNvbGUvLi9zcmMvaG9va3MvcmVkdXgudHM/ODRhMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFJlZHV4IGhvb2tzXG4gKiDmj5DkvpvnsbvlnovlronlhajnmoRkaXNwYXRjaOWSjHNlbGVjdG9yXG4gKi9cblxuaW1wb3J0IHsgVHlwZWRVc2VTZWxlY3Rvckhvb2ssIHVzZURpc3BhdGNoLCB1c2VTZWxlY3RvciB9IGZyb20gJ3JlYWN0LXJlZHV4J1xuaW1wb3J0IHR5cGUgeyBSb290U3RhdGUsIEFwcERpc3BhdGNoIH0gZnJvbSAnQC9zdG9yZSdcblxuLyoqXG4gKiDnsbvlnovlronlhajnmoR1c2VEaXNwYXRjaCBob29rXG4gKi9cbmV4cG9ydCBjb25zdCB1c2VBcHBEaXNwYXRjaCA9ICgpID0+IHVzZURpc3BhdGNoPEFwcERpc3BhdGNoPigpXG5cbi8qKlxuICog57G75Z6L5a6J5YWo55qEdXNlU2VsZWN0b3IgaG9va1xuICovXG5leHBvcnQgY29uc3QgdXNlQXBwU2VsZWN0b3I6IFR5cGVkVXNlU2VsZWN0b3JIb29rPFJvb3RTdGF0ZT4gPSB1c2VTZWxlY3RvciAiXSwibmFtZXMiOlsidXNlRGlzcGF0Y2giLCJ1c2VTZWxlY3RvciIsInVzZUFwcERpc3BhdGNoIiwidXNlQXBwU2VsZWN0b3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/redux.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   ApiService: () => (/* binding */ ApiService),\n/* harmony export */   apiService: () => (/* binding */ apiService)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/constants */ \"(ssr)/./src/constants/index.ts\");\n/**\n * API基础服务类\n * 处理HTTP请求、认证和错误处理\n */ \n/**\n * API基础配置\n */ const API_CONFIG = {\n    baseURL: \"http://helloapi.lightrain.vip/b/\",\n    timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || \"30000\"),\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n};\n/**\n * API错误类\n */ class ApiError extends Error {\n    constructor(status, message, data){\n        super(message);\n        this.name = \"ApiError\";\n        this.status = status;\n        this.data = data;\n    }\n}\n/**\n * API基础服务类\n */ class ApiService {\n    constructor(){\n        this.baseURL = API_CONFIG.baseURL;\n        this.timeout = API_CONFIG.timeout;\n        this.defaultHeaders = {\n            ...API_CONFIG.headers\n        };\n    }\n    /**\n   * 获取认证token\n   */ getAuthToken() {\n        if (true) return null;\n        return localStorage.getItem(_constants__WEBPACK_IMPORTED_MODULE_0__.AUTH.TOKEN_STORAGE_KEY);\n    }\n    /**\n   * 设置认证token\n   */ setAuthToken(token) {\n        if (false) {}\n    }\n    /**\n   * 清除认证token\n   */ clearAuthToken() {\n        if (false) {}\n    }\n    /**\n   * 获取请求头\n   */ getHeaders(requireAuth = true) {\n        const headers = {\n            ...this.defaultHeaders\n        };\n        if (requireAuth) {\n            const token = this.getAuthToken();\n            if (token) {\n                headers.Authorization = `Bearer ${token}`;\n            }\n        }\n        return headers;\n    }\n    /**\n   * 处理响应\n   */ async handleResponse(response) {\n        if (!response.ok) {\n            let errorData = null;\n            try {\n                errorData = await response.json();\n            } catch (e) {\n                // 无法解析错误信息，忽略错误\n                console.warn(\"无法解析API错误响应:\", e);\n            }\n            const message = errorData?.detail || `HTTP ${response.status}: ${response.statusText}`;\n            // 处理401未授权错误，触发自动登出\n            if (response.status === 401) {\n                // 安全地处理未授权错误\n                if (false) {}\n            }\n            throw new ApiError(response.status, message, errorData);\n        }\n        try {\n            return await response.json();\n        } catch (e) {\n            // 空响应或非JSON响应，返回空对象\n            console.warn(\"响应不是有效的JSON格式:\", e);\n            return {};\n        }\n    }\n    /**\n   * GET请求\n   */ async get(endpoint, params, requireAuth = true) {\n        const url = new URL(endpoint, this.baseURL);\n        // 添加查询参数\n        if (params) {\n            Object.entries(params).forEach(([key, value])=>{\n                if (value !== undefined && value !== null) {\n                    url.searchParams.append(key, String(value));\n                }\n            });\n        }\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), this.timeout);\n        try {\n            const response = await fetch(url.toString(), {\n                method: \"GET\",\n                headers: this.getHeaders(requireAuth),\n                signal: controller.signal\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            if (error instanceof Error && error.name === \"AbortError\") {\n                throw new ApiError(408, \"请求超时\");\n            }\n            if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                throw new ApiError(0, \"网络连接失败，请检查网络设置\");\n            }\n            throw error;\n        } finally{\n            clearTimeout(timeoutId);\n        }\n    }\n    /**\n   * POST请求\n   */ async post(endpoint, data, requireAuth = true) {\n        const url = new URL(endpoint, this.baseURL);\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), this.timeout);\n        try {\n            const response = await fetch(url.toString(), {\n                method: \"POST\",\n                headers: this.getHeaders(requireAuth),\n                body: data ? JSON.stringify(data) : undefined,\n                signal: controller.signal\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            if (error instanceof Error && error.name === \"AbortError\") {\n                throw new ApiError(408, \"请求超时\");\n            }\n            if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                throw new ApiError(0, \"网络连接失败，请检查网络设置\");\n            }\n            throw error;\n        } finally{\n            clearTimeout(timeoutId);\n        }\n    }\n    /**\n   * PUT请求\n   */ async put(endpoint, data, requireAuth = true) {\n        const url = new URL(endpoint, this.baseURL);\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), this.timeout);\n        try {\n            const response = await fetch(url.toString(), {\n                method: \"PUT\",\n                headers: this.getHeaders(requireAuth),\n                body: data ? JSON.stringify(data) : undefined,\n                signal: controller.signal\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            if (error instanceof Error && error.name === \"AbortError\") {\n                throw new ApiError(408, \"请求超时\");\n            }\n            if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                throw new ApiError(0, \"网络连接失败，请检查网络设置\");\n            }\n            throw error;\n        } finally{\n            clearTimeout(timeoutId);\n        }\n    }\n    /**\n   * DELETE请求\n   */ async delete(endpoint, requireAuth = true) {\n        const url = new URL(endpoint, this.baseURL);\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), this.timeout);\n        try {\n            const response = await fetch(url.toString(), {\n                method: \"DELETE\",\n                headers: this.getHeaders(requireAuth),\n                signal: controller.signal\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            if (error instanceof Error && error.name === \"AbortError\") {\n                throw new ApiError(408, \"请求超时\");\n            }\n            if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                throw new ApiError(0, \"网络连接失败，请检查网络设置\");\n            }\n            throw error;\n        } finally{\n            clearTimeout(timeoutId);\n        }\n    }\n    /**\n   * PATCH请求\n   */ async patch(endpoint, data, requireAuth = true) {\n        const url = new URL(endpoint, this.baseURL);\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), this.timeout);\n        try {\n            const response = await fetch(url.toString(), {\n                method: \"PATCH\",\n                headers: this.getHeaders(requireAuth),\n                body: data ? JSON.stringify(data) : undefined,\n                signal: controller.signal\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            if (error instanceof Error && error.name === \"AbortError\") {\n                throw new ApiError(408, \"请求超时\");\n            }\n            if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                throw new ApiError(0, \"网络连接失败，请检查网络设置\");\n            }\n            throw error;\n        } finally{\n            clearTimeout(timeoutId);\n        }\n    }\n}\n// 导出API服务实例\nconst apiService = new ApiService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/userAPI.ts":
/*!*********************************!*\
  !*** ./src/services/userAPI.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserAPI: () => (/* binding */ UserAPI),\n/* harmony export */   userAPI: () => (/* binding */ userAPI)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"(ssr)/./src/services/api.ts\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/constants */ \"(ssr)/./src/constants/index.ts\");\n/* harmony import */ var _types_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/api */ \"(ssr)/./src/types/api.ts\");\n/**\n * 用户管理API服务\n * 包含认证、用户CRUD、权限管理等功能\n */ \n\n\n/**\n * 用户API服务类\n */ class UserAPI {\n    // ==================== 认证相关 ====================\n    /**\n   * 发送管理员登录验证码\n   * @param phone 手机号\n   */ async sendAdminVerificationCode(phone) {\n        const request = {\n            phone,\n            purpose: \"login\"\n        };\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.post(\"api/v1/admin/users/auth/send-code\", request, false // 不需要认证\n        );\n    }\n    /**\n   * 管理员登录\n   * @param phone 手机号\n   * @param verificationCode 验证码\n   */ async adminLogin(phone, verificationCode) {\n        const request = {\n            phone,\n            verification_code: verificationCode\n        };\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiService.post(\"api/v1/admin/users/auth/login\", request, false // 不需要认证\n        );\n        // 登录成功后设置token并获取权限\n        if (response.access_token) {\n            // 先设置token，这样后续的API调用才能成功\n            _api__WEBPACK_IMPORTED_MODULE_0__.apiService.setAuthToken(response.access_token);\n            // 设置token过期计时器\n            if (response.expires_in) {\n                const { setupTokenExpirationTimer } = __webpack_require__(/*! @/utils/authUtils */ \"(ssr)/./src/utils/authUtils.ts\");\n                setupTokenExpirationTimer(response.expires_in);\n            }\n            const permissions = response.user?.permissions || [];\n            const permissionCodes = permissions.map((permission)=>permission.code);\n            try {\n                // 合并用户信息和权限数据\n                const userWithPermissions = {\n                    ...response.user,\n                    roles: response.user?.roles || [],\n                    permissions: permissions,\n                    permission_codes: permissionCodes\n                };\n                // 存储完整的用户信息\n                if (false) {}\n            } catch (error) {\n                // 即使权限获取失败，也要存储基本用户信息\n                if (false) {}\n            }\n        }\n        return response;\n    }\n    /**\n   * 退出登录\n   */ async logout() {\n        // 清除本地存储的认证信息\n        _api__WEBPACK_IMPORTED_MODULE_0__.apiService.clearAuthToken();\n        // 清除token过期计时器\n        if (false) {}\n    }\n    /**\n   * 获取当前用户信息\n   */ getCurrentUser() {\n        if (true) return null;\n        const userStr = localStorage.getItem(_constants__WEBPACK_IMPORTED_MODULE_1__.AUTH.USER_STORAGE_KEY);\n        return userStr ? JSON.parse(userStr) : null;\n    }\n    /**\n   * 获取当前用户详细信息\n   * 包含完整的角色和权限信息\n   */ async getCurrentUserProfile() {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.get(\"api/v1/admin/users/me/profile\");\n    }\n    /**\n   * 更新当前用户个人信息\n   * @param userData 更新数据\n   */ async updateCurrentUserProfile(userData) {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.put(\"api/v1/admin/users/me/profile\", userData);\n    }\n    /**\n   * 获取当前用户权限信息\n   */ async getCurrentUserPermissions() {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.get(\"api/v1/admin/permissions/me\");\n    }\n    // ==================== 用户管理 ====================\n    /**\n   * 获取用户列表\n   * @param params 查询参数\n   */ async getUsers(params = {}) {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.get(\"api/v1/admin/users/\", params);\n    }\n    /**\n   * 获取用户详情\n   * @param userId 用户ID\n   */ async getUserDetail(userId) {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.get(`api/v1/admin/users/${userId}`);\n    }\n    /**\n   * 创建用户\n   * @param userData 用户数据\n   */ async createUser(userData) {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.post(\"api/v1/admin/users/\", userData);\n    }\n    /**\n   * 更新用户信息\n   * @param userId 用户ID\n   * @param userData 更新数据\n   */ async updateUser(userId, userData) {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.put(`api/v1/admin/users/${userId}`, userData);\n    }\n    /**\n   * 更新用户状态\n   * @param userId 用户ID\n   * @param isActive 是否激活\n   */ async updateUserStatus(userId, isActive) {\n        const request = {\n            is_active: isActive\n        };\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.put(`api/v1/admin/users/${userId}/status`, request);\n    }\n    /**\n   * 删除用户\n   * @param userId 用户ID\n   */ async deleteUser(userId) {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.delete(`api/v1/admin/users/${userId}`);\n    }\n    // ==================== 权限管理 ====================\n    /**\n   * 获取用户角色\n   * @param userId 用户ID\n   */ async getUserRoles(userId) {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.get(`api/v1/admin/permissions/users/${userId}/roles`);\n    }\n    /**\n   * 为用户分配角色\n   * @param userId 用户ID\n   * @param roleIds 角色ID列表\n   */ async assignUserRoles(userId, roleIds) {\n        const request = {\n            role_ids: roleIds\n        };\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.post(`api/v1/admin/permissions/users/${userId}/roles`, request);\n    }\n    /**\n   * 撤销用户角色\n   * @param userId 用户ID\n   * @param roleId 角色ID\n   */ async revokeUserRole(userId, roleId) {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.delete(`api/v1/admin/permissions/users/${userId}/roles/${roleId}`);\n    }\n    /**\n   * 获取用户权限\n   * @param userId 用户ID\n   */ async getUserPermissions(userId) {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.get(`api/v1/admin/permissions/users/${userId}/permissions`);\n    }\n    // ==================== 工具方法 ====================\n    /**\n   * 获取所有角色\n   */ async getAllRoles() {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.get(\"api/v1/admin/permissions/roles/\");\n    }\n    /**\n   * 获取角色详情\n   * @param roleId 角色ID\n   */ async getRoleDetail(roleId) {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.apiService.get(`api/v1/admin/permissions/roles/${roleId}`);\n    }\n    /**\n   * 检查是否已认证\n   */ isAuthenticated() {\n        if (true) return false;\n        const token = localStorage.getItem(_constants__WEBPACK_IMPORTED_MODULE_1__.AUTH.TOKEN_STORAGE_KEY);\n        const user = localStorage.getItem(_constants__WEBPACK_IMPORTED_MODULE_1__.AUTH.USER_STORAGE_KEY);\n        return !!(token && user);\n    }\n    /**\n   * 格式化用户类型\n   * @param userType 用户类型\n   */ formatUserType(userType) {\n        switch(userType){\n            case _types_api__WEBPACK_IMPORTED_MODULE_2__.UserType.NOVICE:\n                return \"小白型\";\n            case _types_api__WEBPACK_IMPORTED_MODULE_2__.UserType.ADVANCED:\n                return \"进阶型\";\n            case _types_api__WEBPACK_IMPORTED_MODULE_2__.UserType.ANXIOUS:\n                return \"焦虑型\";\n            default:\n                return \"未知类型\";\n        }\n    }\n    /**\n   * 格式化风险等级\n   * @param riskLevel 风险等级\n   */ formatRiskLevel(riskLevel) {\n        switch(riskLevel){\n            case _types_api__WEBPACK_IMPORTED_MODULE_2__.RiskLevel.CONSERVATIVE:\n                return \"保守型\";\n            case _types_api__WEBPACK_IMPORTED_MODULE_2__.RiskLevel.MODERATE_CONSERVATIVE:\n                return \"稳健偏保守型\";\n            case _types_api__WEBPACK_IMPORTED_MODULE_2__.RiskLevel.MODERATE:\n                return \"稳健型\";\n            case _types_api__WEBPACK_IMPORTED_MODULE_2__.RiskLevel.MODERATE_AGGRESSIVE:\n                return \"稳健偏进取型\";\n            case _types_api__WEBPACK_IMPORTED_MODULE_2__.RiskLevel.AGGRESSIVE:\n                return \"进取型\";\n            default:\n                return \"未知类型\";\n        }\n    }\n    /**\n   * 格式化金融知识水平\n   * @param level 金融知识水平\n   */ formatKnowledgeLevel(level) {\n        switch(level){\n            case _types_api__WEBPACK_IMPORTED_MODULE_2__.KnowledgeLevel.BASIC:\n                return \"基础水平\";\n            case _types_api__WEBPACK_IMPORTED_MODULE_2__.KnowledgeLevel.INTERMEDIATE:\n                return \"中级水平\";\n            case _types_api__WEBPACK_IMPORTED_MODULE_2__.KnowledgeLevel.ADVANCED:\n                return \"高级水平\";\n            case _types_api__WEBPACK_IMPORTED_MODULE_2__.KnowledgeLevel.EXPERT:\n                return \"专家水平\";\n            case _types_api__WEBPACK_IMPORTED_MODULE_2__.KnowledgeLevel.PROFESSIONAL:\n                return \"专业水平\";\n            default:\n                return \"未知水平\";\n        }\n    }\n    /**\n   * 格式化用户状态\n   * @param isActive 是否激活\n   * @param isVerified 是否验证\n   */ formatUserStatus(isActive, isVerified) {\n        if (!isActive) {\n            return {\n                text: \"已禁用\",\n                color: \"error\"\n            };\n        }\n        if (!isVerified) {\n            return {\n                text: \"未验证\",\n                color: \"warning\"\n            };\n        }\n        return {\n            text: \"正常\",\n            color: \"success\"\n        };\n    }\n}\n/**\n * 导出用户API服务实例\n */ const userAPI = new UserAPI();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/userAPI.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/index.ts":
/*!****************************!*\
  !*** ./src/store/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   store: () => (/* binding */ store),\n/* harmony export */   useAppDispatch: () => (/* binding */ useAppDispatch),\n/* harmony export */   useAppSelector: () => (/* binding */ useAppSelector)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.esm.js\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/es/index.js\");\n/* harmony import */ var _slices_authSlice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./slices/authSlice */ \"(ssr)/./src/store/slices/authSlice.ts\");\n/* harmony import */ var _slices_uiSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./slices/uiSlice */ \"(ssr)/./src/store/slices/uiSlice.ts\");\n/* harmony import */ var _slices_dashboardSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./slices/dashboardSlice */ \"(ssr)/./src/store/slices/dashboardSlice.ts\");\n\n\n// Import reducers (使用简化版本避免循环依赖)\n\n\n\n/**\n * Redux store配置\n * 配置全局状态管理\n */ const store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_4__.configureStore)({\n    reducer: {\n        auth: _slices_authSlice__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        ui: _slices_uiSlice__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        dashboard: _slices_dashboardSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({\n            serializableCheck: {\n                ignoredActions: [\n                    \"persist/PERSIST\",\n                    \"persist/REHYDRATE\"\n                ]\n            }\n        }),\n    devTools: \"development\" !== \"production\"\n});\n// 类型化的hooks\nconst useAppDispatch = ()=>(0,react_redux__WEBPACK_IMPORTED_MODULE_0__.useDispatch)();\nconst useAppSelector = react_redux__WEBPACK_IMPORTED_MODULE_0__.useSelector;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3RvcmUvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBaUQ7QUFDMkI7QUFFNUUsaUNBQWlDO0FBQzBCO0FBQ047QUFDcUI7QUFXMUU7OztDQUdDLEdBQ00sTUFBTU0sUUFBUU4sZ0VBQWNBLENBQUM7SUFDbENPLFNBQVM7UUFDUEMsTUFBTUwseURBQVdBO1FBQ2pCTSxJQUFJTCx1REFBU0E7UUFDYk0sV0FBV0wsOERBQWdCQTtJQUM3QjtJQUNBTSxZQUFZLENBQUNDLHVCQUNYQSxxQkFBcUI7WUFDbkJDLG1CQUFtQjtnQkFDakJDLGdCQUFnQjtvQkFBQztvQkFBbUI7aUJBQW9CO1lBQzFEO1FBQ0Y7SUFDRkMsVUFBVUMsa0JBQXlCO0FBQ3JDLEdBQUU7QUFJRixZQUFZO0FBQ0wsTUFBTUMsaUJBQWlCLElBQU1oQix3REFBV0EsR0FBZTtBQUN2RCxNQUFNaUIsaUJBQWtEaEIsb0RBQVdBLENBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9maW5zaWdodC1jb25zb2xlLy4vc3JjL3N0b3JlL2luZGV4LnRzP2NlZTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29uZmlndXJlU3RvcmUgfSBmcm9tICdAcmVkdXhqcy90b29sa2l0J1xuaW1wb3J0IHsgVHlwZWRVc2VTZWxlY3Rvckhvb2ssIHVzZURpc3BhdGNoLCB1c2VTZWxlY3RvciB9IGZyb20gJ3JlYWN0LXJlZHV4J1xuXG4vLyBJbXBvcnQgcmVkdWNlcnMgKOS9v+eUqOeugOWMlueJiOacrOmBv+WFjeW+queOr+S+nei1lilcbmltcG9ydCBhdXRoUmVkdWNlciwgeyBBdXRoU3RhdGUgfSBmcm9tICcuL3NsaWNlcy9hdXRoU2xpY2UnXG5pbXBvcnQgdWlSZWR1Y2VyLCB7IFVJU3RhdGUgfSBmcm9tICcuL3NsaWNlcy91aVNsaWNlJ1xuaW1wb3J0IGRhc2hib2FyZFJlZHVjZXIsIHsgRGFzaGJvYXJkU3RhdGUgfSBmcm9tICcuL3NsaWNlcy9kYXNoYm9hcmRTbGljZSdcblxuLyoqXG4gKiBTdG9yZeeKtuaAgeexu+Wei1xuICovXG5leHBvcnQgaW50ZXJmYWNlIFJvb3RTdGF0ZSB7XG4gIGF1dGg6IEF1dGhTdGF0ZVxuICB1aTogVUlTdGF0ZVxuICBkYXNoYm9hcmQ6IERhc2hib2FyZFN0YXRlXG59XG5cbi8qKlxuICogUmVkdXggc3RvcmXphY3nva5cbiAqIOmFjee9ruWFqOWxgOeKtuaAgeeuoeeQhlxuICovXG5leHBvcnQgY29uc3Qgc3RvcmUgPSBjb25maWd1cmVTdG9yZSh7XG4gIHJlZHVjZXI6IHtcbiAgICBhdXRoOiBhdXRoUmVkdWNlcixcbiAgICB1aTogdWlSZWR1Y2VyLFxuICAgIGRhc2hib2FyZDogZGFzaGJvYXJkUmVkdWNlcixcbiAgfSxcbiAgbWlkZGxld2FyZTogKGdldERlZmF1bHRNaWRkbGV3YXJlKSA9PlxuICAgIGdldERlZmF1bHRNaWRkbGV3YXJlKHtcbiAgICAgIHNlcmlhbGl6YWJsZUNoZWNrOiB7XG4gICAgICAgIGlnbm9yZWRBY3Rpb25zOiBbJ3BlcnNpc3QvUEVSU0lTVCcsICdwZXJzaXN0L1JFSFlEUkFURSddLFxuICAgICAgfSxcbiAgICB9KSxcbiAgZGV2VG9vbHM6IHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicsXG59KVxuXG5leHBvcnQgdHlwZSBBcHBEaXNwYXRjaCA9IHR5cGVvZiBzdG9yZS5kaXNwYXRjaFxuXG4vLyDnsbvlnovljJbnmoRob29rc1xuZXhwb3J0IGNvbnN0IHVzZUFwcERpc3BhdGNoID0gKCkgPT4gdXNlRGlzcGF0Y2g8QXBwRGlzcGF0Y2g+KClcbmV4cG9ydCBjb25zdCB1c2VBcHBTZWxlY3RvcjogVHlwZWRVc2VTZWxlY3Rvckhvb2s8Um9vdFN0YXRlPiA9IHVzZVNlbGVjdG9yICJdLCJuYW1lcyI6WyJjb25maWd1cmVTdG9yZSIsInVzZURpc3BhdGNoIiwidXNlU2VsZWN0b3IiLCJhdXRoUmVkdWNlciIsInVpUmVkdWNlciIsImRhc2hib2FyZFJlZHVjZXIiLCJzdG9yZSIsInJlZHVjZXIiLCJhdXRoIiwidWkiLCJkYXNoYm9hcmQiLCJtaWRkbGV3YXJlIiwiZ2V0RGVmYXVsdE1pZGRsZXdhcmUiLCJzZXJpYWxpemFibGVDaGVjayIsImlnbm9yZWRBY3Rpb25zIiwiZGV2VG9vbHMiLCJwcm9jZXNzIiwidXNlQXBwRGlzcGF0Y2giLCJ1c2VBcHBTZWxlY3RvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/store/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/slices/authSlice.ts":
/*!***************************************!*\
  !*** ./src/store/slices/authSlice.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearError: () => (/* binding */ clearError),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   phoneLogin: () => (/* binding */ phoneLogin),\n/* harmony export */   restoreAuthState: () => (/* binding */ restoreAuthState),\n/* harmony export */   sendSmsCode: () => (/* binding */ sendSmsCode),\n/* harmony export */   updateUser: () => (/* binding */ updateUser)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.esm.js\");\n/* harmony import */ var _services_userAPI__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/userAPI */ \"(ssr)/./src/services/userAPI.ts\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/constants */ \"(ssr)/./src/constants/index.ts\");\n\n\n\n/**\n * 初始状态\n */ const initialState = {\n    user: null,\n    token: null,\n    isAuthenticated: false,\n    loading: false,\n    error: null,\n    permissions: [],\n    roles: []\n};\n// ==================== 异步actions ====================\n/**\n * 发送短信验证码\n */ const sendSmsCode = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_2__.createAsyncThunk)(\"auth/sendSmsCode\", async ({ phone }, { rejectWithValue })=>{\n    try {\n        const response = await _services_userAPI__WEBPACK_IMPORTED_MODULE_0__.userAPI.sendAdminVerificationCode(phone);\n        return response;\n    } catch (error) {\n        const apiError = error;\n        return rejectWithValue(apiError.message);\n    }\n});\n/**\n * 手机号验证码登录\n */ const phoneLogin = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_2__.createAsyncThunk)(\"auth/phoneLogin\", async ({ phone, verification_code }, { rejectWithValue })=>{\n    try {\n        const response = await _services_userAPI__WEBPACK_IMPORTED_MODULE_0__.userAPI.adminLogin(phone, verification_code);\n        return response;\n    } catch (error) {\n        const apiError = error;\n        return rejectWithValue(apiError.message);\n    }\n});\n/**\n * 恢复认证状态\n */ const restoreAuthState = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_2__.createAsyncThunk)(\"auth/restoreAuthState\", async (_, { rejectWithValue })=>{\n    try {\n        // 从localStorage恢复用户信息和token\n        const token =  false ? 0 : null;\n        const userStr =  false ? 0 : null;\n        if (!token || !userStr) {\n            throw new Error(\"No stored auth data\");\n        }\n        // 检查token是否已过期\n        if (false) {}\n        const user = JSON.parse(userStr);\n        // 确保API服务也有token\n        if (false) {}\n        return {\n            user,\n            token\n        };\n    } catch (error) {\n        return rejectWithValue(\"Failed to restore auth state\");\n    }\n});\n/**\n * 退出登录\n */ const logout = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_2__.createAsyncThunk)(\"auth/logout\", async ()=>{\n    try {\n        await _services_userAPI__WEBPACK_IMPORTED_MODULE_0__.userAPI.logout();\n        return true;\n    } catch (error) {\n        // 即使API调用失败，也要清除本地状态\n        return true;\n    }\n});\n// ==================== Slice ====================\n/**\n * 认证状态slice\n * 管理用户登录、登出等状态\n */ const authSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_2__.createSlice)({\n    name: \"auth\",\n    initialState,\n    reducers: {\n        /**\n     * 清除错误\n     */ clearError: (state)=>{\n            state.error = null;\n        },\n        /**\n     * 更新用户信息\n     */ updateUser: (state, action)=>{\n            if (state.user) {\n                state.user = {\n                    ...state.user,\n                    ...action.payload\n                };\n            }\n        }\n    },\n    extraReducers: (builder)=>{\n        // 发送短信验证码\n        builder.addCase(sendSmsCode.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(sendSmsCode.fulfilled, (state)=>{\n            state.loading = false;\n            state.error = null;\n        }).addCase(sendSmsCode.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n        // 手机号登录\n        builder.addCase(phoneLogin.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(phoneLogin.fulfilled, (state, action)=>{\n            const { user, access_token } = action.payload;\n            // 将AdminUserResponse转换为AuthState中期望的AdminUser类型\n            const adminUser = {\n                id: user.id,\n                phone: user.phone,\n                username: user.username || undefined,\n                email: user.email || undefined,\n                is_active: user.is_active,\n                created_at: user.created_at,\n                updated_at: user.updated_at\n            };\n            state.loading = false;\n            state.isAuthenticated = true;\n            state.user = adminUser;\n            state.token = access_token;\n            state.permissions = user.permissions?.map((p)=>p.code) || [];\n            state.roles = user.roles || [];\n            state.error = null;\n        }).addCase(phoneLogin.rejected, (state, action)=>{\n            state.loading = false;\n            state.isAuthenticated = false;\n            state.user = null;\n            state.token = null;\n            state.permissions = [];\n            state.roles = [];\n            state.error = action.payload;\n        });\n        // 恢复认证状态\n        builder.addCase(restoreAuthState.pending, (state)=>{\n            state.loading = true;\n        }).addCase(restoreAuthState.fulfilled, (state, action)=>{\n            const { user, token } = action.payload;\n            state.loading = false;\n            state.isAuthenticated = true;\n            state.user = user;\n            state.token = token;\n            // 恢复权限和角色信息\n            state.permissions = user.permission_codes || user.permissions?.map((p)=>p.code) || [];\n            state.roles = user.roles || [];\n            state.error = null;\n        }).addCase(restoreAuthState.rejected, (state)=>{\n            state.loading = false;\n            state.isAuthenticated = false;\n            state.user = null;\n            state.token = null;\n            state.permissions = [];\n            state.roles = [];\n            state.error = null;\n        });\n        // 退出登录\n        builder.addCase(logout.fulfilled, (state)=>{\n            state.isAuthenticated = false;\n            state.user = null;\n            state.token = null;\n            state.permissions = [];\n            state.roles = [];\n            state.error = null;\n            state.loading = false;\n        });\n    }\n});\nconst { clearError, updateUser } = authSlice.actions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authSlice.reducer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/slices/authSlice.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/slices/dashboardSlice.ts":
/*!********************************************!*\
  !*** ./src/store/slices/dashboardSlice.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addActivity: () => (/* binding */ addActivity),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   resetDashboard: () => (/* binding */ resetDashboard),\n/* harmony export */   setError: () => (/* binding */ setError),\n/* harmony export */   setLoading: () => (/* binding */ setLoading),\n/* harmony export */   updateDateRange: () => (/* binding */ updateDateRange),\n/* harmony export */   updateOrderChart: () => (/* binding */ updateOrderChart),\n/* harmony export */   updateRecentActivities: () => (/* binding */ updateRecentActivities),\n/* harmony export */   updateRevenueChart: () => (/* binding */ updateRevenueChart),\n/* harmony export */   updateStats: () => (/* binding */ updateStats),\n/* harmony export */   updateUserChart: () => (/* binding */ updateUserChart)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.esm.js\");\n\n/**\n * 初始状态\n */ const initialState = {\n    stats: null,\n    revenueChart: [],\n    userChart: [],\n    orderChart: [],\n    recentActivities: [],\n    isLoading: false,\n    error: null,\n    dateRange: {\n        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),\n        end: new Date().toISOString()\n    }\n};\n/**\n * 仪表板状态slice\n * 管理仪表板数据和状态\n */ const dashboardSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: \"dashboard\",\n    initialState,\n    reducers: {\n        /**\n     * 开始加载数据\n     */ setLoading: (state, action)=>{\n            state.isLoading = action.payload;\n            if (action.payload) {\n                state.error = null;\n            }\n        },\n        /**\n     * 设置错误\n     */ setError: (state, action)=>{\n            state.error = action.payload;\n            state.isLoading = false;\n        },\n        /**\n     * 更新统计数据\n     */ updateStats: (state, action)=>{\n            state.stats = action.payload;\n        },\n        /**\n     * 更新营收图表数据\n     */ updateRevenueChart: (state, action)=>{\n            state.revenueChart = action.payload;\n        },\n        /**\n     * 更新用户图表数据\n     */ updateUserChart: (state, action)=>{\n            state.userChart = action.payload;\n        },\n        /**\n     * 更新订单图表数据\n     */ updateOrderChart: (state, action)=>{\n            state.orderChart = action.payload;\n        },\n        /**\n     * 更新最近活动\n     */ updateRecentActivities: (state, action)=>{\n            state.recentActivities = action.payload;\n        },\n        /**\n     * 添加新活动\n     */ addActivity: (state, action)=>{\n            state.recentActivities.unshift(action.payload);\n            // 只保留最近20条活动记录\n            if (state.recentActivities.length > 20) {\n                state.recentActivities = state.recentActivities.slice(0, 20);\n            }\n        },\n        /**\n     * 更新日期范围\n     */ updateDateRange: (state, action)=>{\n            state.dateRange = action.payload;\n        },\n        /**\n     * 重置状态\n     */ resetDashboard: (state)=>{\n            return {\n                ...initialState,\n                dateRange: state.dateRange\n            };\n        }\n    }\n});\nconst { setLoading, setError, updateStats, updateRevenueChart, updateUserChart, updateOrderChart, updateRecentActivities, addActivity, updateDateRange, resetDashboard } = dashboardSlice.actions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dashboardSlice.reducer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/slices/dashboardSlice.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/slices/uiSlice.ts":
/*!*************************************!*\
  !*** ./src/store/slices/uiSlice.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addNotification: () => (/* binding */ addNotification),\n/* harmony export */   clearNotifications: () => (/* binding */ clearNotifications),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   removeNotification: () => (/* binding */ removeNotification),\n/* harmony export */   setGlobalLoading: () => (/* binding */ setGlobalLoading),\n/* harmony export */   setLoading: () => (/* binding */ setLoading),\n/* harmony export */   setSidebarCollapsed: () => (/* binding */ setSidebarCollapsed),\n/* harmony export */   setTheme: () => (/* binding */ setTheme),\n/* harmony export */   toggleSidebar: () => (/* binding */ toggleSidebar),\n/* harmony export */   toggleTheme: () => (/* binding */ toggleTheme)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.esm.js\");\n\n/**\n * 初始状态\n */ const initialState = {\n    sidebarCollapsed: false,\n    theme: \"dark\",\n    loading: {\n        global: false\n    },\n    notifications: []\n};\n/**\n * UI状态slice\n * 管理界面相关状态\n */ const uiSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: \"ui\",\n    initialState,\n    reducers: {\n        /**\n     * 切换侧边栏收起状态\n     */ toggleSidebar: (state)=>{\n            state.sidebarCollapsed = !state.sidebarCollapsed;\n        },\n        /**\n     * 设置侧边栏状态\n     */ setSidebarCollapsed: (state, action)=>{\n            state.sidebarCollapsed = action.payload;\n        },\n        /**\n     * 切换主题\n     */ toggleTheme: (state)=>{\n            state.theme = state.theme === \"dark\" ? \"light\" : \"dark\";\n        },\n        /**\n     * 设置主题\n     */ setTheme: (state, action)=>{\n            state.theme = action.payload;\n        },\n        /**\n     * 设置加载状态\n     */ setLoading: (state, action)=>{\n            const { key, loading } = action.payload;\n            state.loading[key] = loading;\n        },\n        /**\n     * 设置全局加载状态\n     */ setGlobalLoading: (state, action)=>{\n            state.loading.global = action.payload;\n        },\n        /**\n     * 添加通知\n     */ addNotification: (state, action)=>{\n            const notification = {\n                id: Date.now().toString(),\n                timestamp: Date.now(),\n                duration: 4500,\n                ...action.payload\n            };\n            state.notifications.unshift(notification);\n        },\n        /**\n     * 移除通知\n     */ removeNotification: (state, action)=>{\n            state.notifications = state.notifications.filter((notification)=>notification.id !== action.payload);\n        },\n        /**\n     * 清除所有通知\n     */ clearNotifications: (state)=>{\n            state.notifications = [];\n        }\n    }\n});\nconst { toggleSidebar, setSidebarCollapsed, toggleTheme, setTheme, setLoading, setGlobalLoading, addNotification, removeNotification, clearNotifications } = uiSlice.actions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (uiSlice.reducer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/slices/uiSlice.ts\n");

/***/ }),

/***/ "(ssr)/./src/styles/antdTheme.ts":
/*!*********************************!*\
  !*** ./src/styles/antdTheme.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   antdTheme: () => (/* binding */ antdTheme)\n/* harmony export */ });\n/**\n * Ant Design 深色主题配置\n * 遵循 docs/UI/global.md 设计规范\n */ const antdTheme = {\n    algorithm: [],\n    token: {\n        // === 颜色系统 ===\n        colorPrimary: \"#4f46e5\",\n        colorSuccess: \"#10b981\",\n        colorWarning: \"#f59e0b\",\n        colorError: \"#ef4444\",\n        colorInfo: \"#3b82f6\",\n        // === 背景色 ===\n        colorBgBase: \"#0c0c0c\",\n        colorBgContainer: \"#111111\",\n        colorBgElevated: \"#1a1a1a\",\n        colorBgLayout: \"#0c0c0c\",\n        colorBgSpotlight: \"#1a1a1a\",\n        colorBgMask: \"rgba(0, 0, 0, 0.6)\",\n        // === 文字色 ===\n        colorText: \"#ffffff\",\n        colorTextSecondary: \"#a3a3a3\",\n        colorTextTertiary: \"#666666\",\n        colorTextQuaternary: \"#555555\",\n        // === 边框色 ===\n        colorBorder: \"#333333\",\n        colorBorderSecondary: \"#555555\",\n        // === 字体 ===\n        fontSize: 16,\n        fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif',\n        // === 圆角 ===\n        borderRadius: 8,\n        borderRadiusLG: 16,\n        borderRadiusSM: 6,\n        // === 阴影 ===\n        boxShadow: \"0 2px 8px rgba(0, 0, 0, 0.1)\",\n        boxShadowSecondary: \"0 4px 12px rgba(0, 0, 0, 0.15)\",\n        boxShadowTertiary: \"0 8px 32px rgba(0, 0, 0, 0.4)\",\n        // === 间距 ===\n        padding: 16,\n        paddingLG: 24,\n        paddingSM: 8,\n        paddingXS: 4,\n        margin: 16,\n        marginLG: 24,\n        marginSM: 8,\n        marginXS: 4,\n        // === 控件尺寸 ===\n        controlHeight: 40,\n        controlHeightLG: 48,\n        controlHeightSM: 32,\n        // === 动画 ===\n        motionDurationSlow: \"0.5s\",\n        motionDurationMid: \"0.3s\",\n        motionDurationFast: \"0.15s\",\n        // === 其他 ===\n        wireframe: false,\n        zIndexPopupBase: 1000\n    },\n    components: {\n        // === 按钮组件 ===\n        Button: {\n            primaryShadow: \"0 2px 8px rgba(79, 70, 229, 0.3)\",\n            defaultShadow: \"0 2px 8px rgba(0, 0, 0, 0.1)\",\n            borderRadius: 8,\n            paddingInline: 24,\n            fontWeight: 500\n        },\n        // === 输入框组件 ===\n        Input: {\n            borderRadius: 6,\n            paddingBlock: 12,\n            paddingInline: 16,\n            colorBgContainer: \"#1a1a1a\",\n            colorBorder: \"#333333\",\n            colorText: \"#ffffff\",\n            colorTextPlaceholder: \"#555555\",\n            activeBorderColor: \"#4f46e5\",\n            hoverBorderColor: \"#555555\"\n        },\n        // === 卡片组件 ===\n        Card: {\n            borderRadius: 16,\n            paddingLG: 24,\n            colorBgContainer: \"#111111\",\n            colorBorderSecondary: \"#333333\",\n            boxShadowTertiary: \"0 8px 32px rgba(0, 0, 0, 0.4)\"\n        },\n        // === 表格组件 ===\n        Table: {\n            borderRadius: 8,\n            colorBgContainer: \"#111111\",\n            colorBorderSecondary: \"#333333\",\n            colorText: \"#ffffff\",\n            colorTextHeading: \"#ffffff\",\n            headerBg: \"#1a1a1a\",\n            headerColor: \"#ffffff\",\n            rowHoverBg: \"#1a1a1a\"\n        },\n        // === 菜单组件 ===\n        Menu: {\n            colorBgContainer: \"transparent\",\n            colorText: \"#a3a3a3\",\n            colorItemTextSelected: \"#ffffff\",\n            colorItemBgSelected: \"#4f46e5\",\n            colorItemBgHover: \"rgba(79, 70, 229, 0.1)\",\n            borderRadius: 8,\n            itemMarginBlock: 2,\n            itemMarginInline: 8,\n            itemPaddingInline: 16\n        },\n        // === 布局组件 ===\n        Layout: {\n            headerBg: \"#111111\",\n            headerColor: \"#ffffff\",\n            headerPadding: \"0 24px\",\n            siderBg: \"#111111\",\n            bodyBg: \"#0c0c0c\",\n            triggerBg: \"#1a1a1a\",\n            triggerColor: \"#ffffff\"\n        },\n        // === 表单组件 ===\n        Form: {\n            labelColor: \"#a3a3a3\",\n            labelFontSize: 14,\n            itemMarginBottom: 24\n        },\n        // === 模态框组件 ===\n        Modal: {\n            borderRadius: 16,\n            colorBgElevated: \"#111111\",\n            colorText: \"#ffffff\",\n            headerBg: \"transparent\",\n            titleColor: \"#ffffff\",\n            contentBg: \"#111111\"\n        },\n        // === 下拉菜单组件 ===\n        Dropdown: {\n            borderRadius: 8,\n            colorBgElevated: \"#1a1a1a\",\n            colorBorder: \"#333333\",\n            boxShadowSecondary: \"0 4px 12px rgba(0, 0, 0, 0.15)\",\n            paddingBlock: 8\n        },\n        // === 抽屉组件 ===\n        Drawer: {\n            borderRadius: 0\n        },\n        // === 通知组件 ===\n        Notification: {\n            borderRadius: 8,\n            colorBgElevated: \"#1a1a1a\",\n            colorText: \"#ffffff\",\n            colorIcon: \"#4f46e5\"\n        },\n        // === 消息组件 ===\n        Message: {\n            borderRadius: 8,\n            colorBgElevated: \"#1a1a1a\",\n            colorText: \"#ffffff\"\n        },\n        // === 标签组件 ===\n        Tag: {\n            borderRadius: 4,\n            fontSize: 12\n        },\n        // === 分页组件 ===\n        Pagination: {\n            borderRadius: 6,\n            colorBgContainer: \"#1a1a1a\",\n            colorText: \"#ffffff\",\n            colorBorder: \"#333333\",\n            itemActiveBg: \"#4f46e5\",\n            itemLinkBg: \"transparent\"\n        },\n        // === Alert 警告提示组件 ===\n        Alert: {\n            borderRadius: 8,\n            // Info 类型\n            colorInfoBg: \"rgba(59, 130, 246, 0.1)\",\n            colorInfoBorder: \"rgba(59, 130, 246, 0.3)\",\n            colorInfoText: \"#3b82f6\",\n            // Success 类型  \n            colorSuccessBg: \"rgba(16, 185, 129, 0.1)\",\n            colorSuccessBorder: \"rgba(16, 185, 129, 0.3)\",\n            colorSuccessText: \"#10b981\",\n            // Warning 类型\n            colorWarningBg: \"rgba(245, 158, 11, 0.1)\",\n            colorWarningBorder: \"rgba(245, 158, 11, 0.3)\",\n            colorWarningText: \"#f59e0b\",\n            // Error 类型\n            colorErrorBg: \"rgba(239, 68, 68, 0.1)\",\n            colorErrorBorder: \"rgba(239, 68, 68, 0.3)\",\n            colorErrorText: \"#ef4444\",\n            // 通用样式\n            fontSize: 14,\n            lineHeight: 1.5,\n            padding: 12\n        },\n        // === 选择器组件 ===\n        Select: {\n            borderRadius: 6,\n            colorBgContainer: \"#1a1a1a\",\n            colorBgElevated: \"#1a1a1a\",\n            colorText: \"#ffffff\",\n            colorBorder: \"#333333\",\n            optionSelectedBg: \"rgba(79, 70, 229, 0.1)\",\n            optionActiveBg: \"#333333\"\n        },\n        // === 日期选择器组件 ===\n        DatePicker: {\n            borderRadius: 6,\n            colorBgContainer: \"#1a1a1a\",\n            colorBgElevated: \"#1a1a1a\",\n            colorText: \"#ffffff\",\n            colorTextHeading: \"#ffffff\",\n            colorBorder: \"#333333\",\n            cellHoverBg: \"#333333\",\n            cellActiveWithRangeBg: \"rgba(79, 70, 229, 0.1)\"\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/styles/antdTheme.ts\n");

/***/ }),

/***/ "(ssr)/./src/styles/theme.ts":
/*!*****************************!*\
  !*** ./src/styles/theme.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   theme: () => (/* binding */ theme)\n/* harmony export */ });\n/**\n * FinSight Console 深色主题配置\n * 遵循 docs/UI/global.md 设计规范\n */ const theme = {\n    colors: {\n        // 主色调 - 品牌蓝\n        primary: \"#4f46e5\",\n        primaryHover: \"#4338ca\",\n        primaryActive: \"#3730a3\",\n        // 状态色\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\",\n        info: \"#3b82f6\",\n        // 背景色 - 深色主题\n        background: {\n            primary: \"#0c0c0c\",\n            secondary: \"#1a1a1a\",\n            card: \"#111111\",\n            tertiary: \"#262626\",\n            hover: \"#2a2a2a\"\n        },\n        // 文字色 - 深色主题\n        text: {\n            primary: \"#ffffff\",\n            secondary: \"#a3a3a3\",\n            tertiary: \"#666666\",\n            placeholder: \"#555555\"\n        },\n        // 边框色\n        border: {\n            default: \"#333333\",\n            hover: \"#555555\",\n            active: \"#4f46e5\"\n        },\n        // 其他\n        shadow: \"rgba(0, 0, 0, 0.4)\",\n        disabled: \"#1a1a1a\"\n    },\n    typography: {\n        fontSize: {\n            xs: \"12px\",\n            sm: \"14px\",\n            md: \"16px\",\n            lg: \"20px\",\n            xl: \"24px\",\n            xxl: \"28px\"\n        },\n        fontWeight: {\n            normal: 400,\n            medium: 500,\n            semibold: 600,\n            bold: 700\n        },\n        lineHeight: {\n            tight: 1.2,\n            normal: 1.5,\n            relaxed: 1.8\n        }\n    },\n    spacing: {\n        xs: \"4px\",\n        sm: \"8px\",\n        md: \"16px\",\n        lg: \"24px\",\n        xl: \"32px\",\n        xxl: \"48px\"\n    },\n    borderRadius: {\n        sm: \"6px\",\n        md: \"8px\",\n        lg: \"12px\",\n        xl: \"16px\"\n    },\n    shadows: {\n        sm: \"0 2px 8px rgba(0, 0, 0, 0.1)\",\n        md: \"0 4px 12px rgba(0, 0, 0, 0.15)\",\n        lg: \"0 8px 32px rgba(0, 0, 0, 0.4)\"\n    },\n    animation: {\n        duration: {\n            fast: \"150ms\",\n            normal: \"300ms\",\n            slow: \"500ms\"\n        },\n        easing: {\n            standard: \"cubic-bezier(0.4, 0, 0.2, 1)\",\n            enter: \"cubic-bezier(0, 0, 0.2, 1)\",\n            exit: \"cubic-bezier(0.4, 0, 1, 1)\"\n        }\n    },\n    // 响应式断点\n    breakpoints: {\n        mobile: \"768px\",\n        tablet: \"1024px\",\n        desktop: \"1200px\"\n    },\n    zIndex: {\n        dropdown: 1000,\n        modal: 1050,\n        tooltip: 1100,\n        notification: 1200\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3R5bGVzL3RoZW1lLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7O0NBR0MsR0FFTSxNQUFNQSxRQUFRO0lBQ25CQyxRQUFRO1FBQ04sWUFBWTtRQUNaQyxTQUFTO1FBQ1RDLGNBQWM7UUFDZEMsZUFBZTtRQUVmLE1BQU07UUFDTkMsU0FBUztRQUNUQyxTQUFTO1FBQ1RDLE9BQU87UUFDUEMsTUFBTTtRQUVOLGFBQWE7UUFDYkMsWUFBWTtZQUNWUCxTQUFTO1lBQ1RRLFdBQVc7WUFDWEMsTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLE9BQU87UUFDVDtRQUVBLGFBQWE7UUFDYkMsTUFBTTtZQUNKWixTQUFTO1lBQ1RRLFdBQVc7WUFDWEUsVUFBVTtZQUNWRyxhQUFhO1FBQ2Y7UUFFQSxNQUFNO1FBQ05DLFFBQVE7WUFDTkMsU0FBUztZQUNUSixPQUFPO1lBQ1BLLFFBQVE7UUFDVjtRQUVBLEtBQUs7UUFDTEMsUUFBUTtRQUNSQyxVQUFVO0lBQ1o7SUFFQUMsWUFBWTtRQUNWQyxVQUFVO1lBQ1JDLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxLQUFLO1FBQ1A7UUFDQUMsWUFBWTtZQUNWQyxRQUFRO1lBQ1JDLFFBQVE7WUFDUkMsVUFBVTtZQUNWQyxNQUFNO1FBQ1I7UUFDQUMsWUFBWTtZQUNWQyxPQUFPO1lBQ1BMLFFBQVE7WUFDUk0sU0FBUztRQUNYO0lBQ0Y7SUFFQUMsU0FBUztRQUNQZCxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsS0FBSztJQUNQO0lBRUFVLGNBQWM7UUFDWmQsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUFZLFNBQVM7UUFDUGYsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7SUFDTjtJQUVBYyxXQUFXO1FBQ1RDLFVBQVU7WUFDUkMsTUFBTTtZQUNOWixRQUFRO1lBQ1JhLE1BQU07UUFDUjtRQUNBQyxRQUFRO1lBQ05DLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxNQUFNO1FBQ1I7SUFDRjtJQUVBLFFBQVE7SUFDUkMsYUFBYTtRQUNYQyxRQUFRO1FBQ1JDLFFBQVE7UUFDUkMsU0FBUztJQUNYO0lBRUFDLFFBQVE7UUFDTkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsY0FBYztJQUNoQjtBQUNGLEVBQVUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9maW5zaWdodC1jb25zb2xlLy4vc3JjL3N0eWxlcy90aGVtZS50cz81MTYxIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRmluU2lnaHQgQ29uc29sZSDmt7HoibLkuLvpopjphY3nva5cbiAqIOmBteW+qiBkb2NzL1VJL2dsb2JhbC5tZCDorr7orqHop4TojINcbiAqL1xuXG5leHBvcnQgY29uc3QgdGhlbWUgPSB7XG4gIGNvbG9yczoge1xuICAgIC8vIOS4u+iJsuiwgyAtIOWTgeeJjOiTnVxuICAgIHByaW1hcnk6ICcjNGY0NmU1JyxcbiAgICBwcmltYXJ5SG92ZXI6ICcjNDMzOGNhJyxcbiAgICBwcmltYXJ5QWN0aXZlOiAnIzM3MzBhMycsXG4gICAgXG4gICAgLy8g54q25oCB6ImyXG4gICAgc3VjY2VzczogJyMxMGI5ODEnLFxuICAgIHdhcm5pbmc6ICcjZjU5ZTBiJywgXG4gICAgZXJyb3I6ICcjZWY0NDQ0JyxcbiAgICBpbmZvOiAnIzNiODJmNicsXG4gICAgXG4gICAgLy8g6IOM5pmv6ImyIC0g5rex6Imy5Li76aKYXG4gICAgYmFja2dyb3VuZDoge1xuICAgICAgcHJpbWFyeTogJyMwYzBjMGMnLCAgICAgIC8vIOS4u+iDjOaZr1xuICAgICAgc2Vjb25kYXJ5OiAnIzFhMWExYScsICAgIC8vIOasoee6p+iDjOaZr1xuICAgICAgY2FyZDogJyMxMTExMTEnLCAgICAgICAgIC8vIOWNoeeJh+iDjOaZr1xuICAgICAgdGVydGlhcnk6ICcjMjYyNjI2JywgICAgIC8vIOesrOS4iee6p+iDjOaZr1xuICAgICAgaG92ZXI6ICcjMmEyYTJhJywgICAgICAgIC8vIOaCrOWBnOiDjOaZr1xuICAgIH0sXG4gICAgXG4gICAgLy8g5paH5a2X6ImyIC0g5rex6Imy5Li76aKYXG4gICAgdGV4dDoge1xuICAgICAgcHJpbWFyeTogJyNmZmZmZmYnLCAgICAgIC8vIOS4u+aWh+Wtl1xuICAgICAgc2Vjb25kYXJ5OiAnI2EzYTNhMycsICAgIC8vIOasoeimgeaWh+Wtl1xuICAgICAgdGVydGlhcnk6ICcjNjY2NjY2JywgICAgIC8vIOi+heWKqeaWh+Wtl1xuICAgICAgcGxhY2Vob2xkZXI6ICcjNTU1NTU1JywgIC8vIOWNoOS9jeesplxuICAgIH0sXG4gICAgXG4gICAgLy8g6L655qGG6ImyXG4gICAgYm9yZGVyOiB7XG4gICAgICBkZWZhdWx0OiAnIzMzMzMzMycsICAgICAgLy8g6buY6K6k6L655qGGXG4gICAgICBob3ZlcjogJyM1NTU1NTUnLCAgICAgICAgLy8g5oKs5YGc6L655qGGXG4gICAgICBhY3RpdmU6ICcjNGY0NmU1JywgICAgICAgLy8g5r+A5rS76L655qGGXG4gICAgfSxcbiAgICBcbiAgICAvLyDlhbbku5ZcbiAgICBzaGFkb3c6ICdyZ2JhKDAsIDAsIDAsIDAuNCknLFxuICAgIGRpc2FibGVkOiAnIzFhMWExYScsXG4gIH0sXG4gIFxuICB0eXBvZ3JhcGh5OiB7XG4gICAgZm9udFNpemU6IHtcbiAgICAgIHhzOiAnMTJweCcsICAgICAgLy8g6L6F5Yqp5paH5a2XXG4gICAgICBzbTogJzE0cHgnLCAgICAgIC8vIOWwj+aWh+Wtl1xuICAgICAgbWQ6ICcxNnB4JywgICAgICAvLyDmraPmlodcbiAgICAgIGxnOiAnMjBweCcsICAgICAgLy8g5qCH6aKYM1xuICAgICAgeGw6ICcyNHB4JywgICAgICAvLyDmoIfpopgyXG4gICAgICB4eGw6ICcyOHB4JywgICAgIC8vIOagh+mimDFcbiAgICB9LFxuICAgIGZvbnRXZWlnaHQ6IHtcbiAgICAgIG5vcm1hbDogNDAwLFxuICAgICAgbWVkaXVtOiA1MDAsXG4gICAgICBzZW1pYm9sZDogNjAwLFxuICAgICAgYm9sZDogNzAwLFxuICAgIH0sXG4gICAgbGluZUhlaWdodDoge1xuICAgICAgdGlnaHQ6IDEuMixcbiAgICAgIG5vcm1hbDogMS41LFxuICAgICAgcmVsYXhlZDogMS44LFxuICAgIH0sXG4gIH0sXG4gIFxuICBzcGFjaW5nOiB7XG4gICAgeHM6ICc0cHgnLFxuICAgIHNtOiAnOHB4JyxcbiAgICBtZDogJzE2cHgnLFxuICAgIGxnOiAnMjRweCcsXG4gICAgeGw6ICczMnB4JyxcbiAgICB4eGw6ICc0OHB4JyxcbiAgfSxcbiAgXG4gIGJvcmRlclJhZGl1czoge1xuICAgIHNtOiAnNnB4JywgICAgICAgLy8g6L6T5YWl5qGGXG4gICAgbWQ6ICc4cHgnLCAgICAgICAvLyDmjInpkq5cbiAgICBsZzogJzEycHgnLCAgICAgIC8vIOWwj+WNoeeJh1xuICAgIHhsOiAnMTZweCcsICAgICAgLy8g5aSn5Y2h54mHXG4gIH0sXG4gIFxuICBzaGFkb3dzOiB7XG4gICAgc206ICcwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpJyxcbiAgICBtZDogJzAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjE1KScsXG4gICAgbGc6ICcwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC40KScsXG4gIH0sXG4gIFxuICBhbmltYXRpb246IHtcbiAgICBkdXJhdGlvbjoge1xuICAgICAgZmFzdDogJzE1MG1zJywgICAgIC8vIOW/q+mAn1xuICAgICAgbm9ybWFsOiAnMzAwbXMnLCAgIC8vIOagh+WHhlxuICAgICAgc2xvdzogJzUwMG1zJywgICAgIC8vIOaFoumAn1xuICAgIH0sXG4gICAgZWFzaW5nOiB7XG4gICAgICBzdGFuZGFyZDogJ2N1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSknLFxuICAgICAgZW50ZXI6ICdjdWJpYy1iZXppZXIoMCwgMCwgMC4yLCAxKScsXG4gICAgICBleGl0OiAnY3ViaWMtYmV6aWVyKDAuNCwgMCwgMSwgMSknLFxuICAgIH0sXG4gIH0sXG4gIFxuICAvLyDlk43lupTlvI/mlq3ngrlcbiAgYnJlYWtwb2ludHM6IHtcbiAgICBtb2JpbGU6ICc3NjhweCcsXG4gICAgdGFibGV0OiAnMTAyNHB4JyxcbiAgICBkZXNrdG9wOiAnMTIwMHB4JyxcbiAgfSxcbiAgXG4gIHpJbmRleDoge1xuICAgIGRyb3Bkb3duOiAxMDAwLFxuICAgIG1vZGFsOiAxMDUwLFxuICAgIHRvb2x0aXA6IDExMDAsXG4gICAgbm90aWZpY2F0aW9uOiAxMjAwLFxuICB9LFxufSBhcyBjb25zdFxuXG5leHBvcnQgdHlwZSBUaGVtZSA9IHR5cGVvZiB0aGVtZSAiXSwibmFtZXMiOlsidGhlbWUiLCJjb2xvcnMiLCJwcmltYXJ5IiwicHJpbWFyeUhvdmVyIiwicHJpbWFyeUFjdGl2ZSIsInN1Y2Nlc3MiLCJ3YXJuaW5nIiwiZXJyb3IiLCJpbmZvIiwiYmFja2dyb3VuZCIsInNlY29uZGFyeSIsImNhcmQiLCJ0ZXJ0aWFyeSIsImhvdmVyIiwidGV4dCIsInBsYWNlaG9sZGVyIiwiYm9yZGVyIiwiZGVmYXVsdCIsImFjdGl2ZSIsInNoYWRvdyIsImRpc2FibGVkIiwidHlwb2dyYXBoeSIsImZvbnRTaXplIiwieHMiLCJzbSIsIm1kIiwibGciLCJ4bCIsInh4bCIsImZvbnRXZWlnaHQiLCJub3JtYWwiLCJtZWRpdW0iLCJzZW1pYm9sZCIsImJvbGQiLCJsaW5lSGVpZ2h0IiwidGlnaHQiLCJyZWxheGVkIiwic3BhY2luZyIsImJvcmRlclJhZGl1cyIsInNoYWRvd3MiLCJhbmltYXRpb24iLCJkdXJhdGlvbiIsImZhc3QiLCJzbG93IiwiZWFzaW5nIiwic3RhbmRhcmQiLCJlbnRlciIsImV4aXQiLCJicmVha3BvaW50cyIsIm1vYmlsZSIsInRhYmxldCIsImRlc2t0b3AiLCJ6SW5kZXgiLCJkcm9wZG93biIsIm1vZGFsIiwidG9vbHRpcCIsIm5vdGlmaWNhdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/styles/theme.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/api.ts":
/*!**************************!*\
  !*** ./src/types/api.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessDataType: () => (/* binding */ BusinessDataType),\n/* harmony export */   CollectionMethod: () => (/* binding */ CollectionMethod),\n/* harmony export */   ContentCategory: () => (/* binding */ ContentCategory),\n/* harmony export */   CrawlMode: () => (/* binding */ CrawlMode),\n/* harmony export */   DataSourceStatus: () => (/* binding */ DataSourceStatus),\n/* harmony export */   KnowledgeLevel: () => (/* binding */ KnowledgeLevel),\n/* harmony export */   LifecycleStage: () => (/* binding */ LifecycleStage),\n/* harmony export */   ModelProvider: () => (/* binding */ ModelProvider),\n/* harmony export */   ModelStatus: () => (/* binding */ ModelStatus),\n/* harmony export */   ModelType: () => (/* binding */ ModelType),\n/* harmony export */   RiskLevel: () => (/* binding */ RiskLevel),\n/* harmony export */   UserType: () => (/* binding */ UserType)\n/* harmony export */ });\n/**\n * API相关类型定义\n */ var UserType;\n(function(UserType) {\n    UserType[UserType[\"NOVICE\"] = 1] = \"NOVICE\";\n    UserType[UserType[\"ADVANCED\"] = 2] = \"ADVANCED\";\n    UserType[UserType[\"ANXIOUS\"] = 3] = \"ANXIOUS\"; // 焦虑型\n})(UserType || (UserType = {}));\nvar RiskLevel;\n(function(RiskLevel) {\n    RiskLevel[RiskLevel[\"CONSERVATIVE\"] = 1] = \"CONSERVATIVE\";\n    RiskLevel[RiskLevel[\"MODERATE_CONSERVATIVE\"] = 2] = \"MODERATE_CONSERVATIVE\";\n    RiskLevel[RiskLevel[\"MODERATE\"] = 3] = \"MODERATE\";\n    RiskLevel[RiskLevel[\"MODERATE_AGGRESSIVE\"] = 4] = \"MODERATE_AGGRESSIVE\";\n    RiskLevel[RiskLevel[\"AGGRESSIVE\"] = 5] = \"AGGRESSIVE\"; // 进取型\n})(RiskLevel || (RiskLevel = {}));\nvar KnowledgeLevel;\n(function(KnowledgeLevel) {\n    KnowledgeLevel[KnowledgeLevel[\"BASIC\"] = 1] = \"BASIC\";\n    KnowledgeLevel[KnowledgeLevel[\"INTERMEDIATE\"] = 2] = \"INTERMEDIATE\";\n    KnowledgeLevel[KnowledgeLevel[\"ADVANCED\"] = 3] = \"ADVANCED\";\n    KnowledgeLevel[KnowledgeLevel[\"EXPERT\"] = 4] = \"EXPERT\";\n    KnowledgeLevel[KnowledgeLevel[\"PROFESSIONAL\"] = 5] = \"PROFESSIONAL\"; // 专业水平：具有金融行业资深经验或专业认证\n})(KnowledgeLevel || (KnowledgeLevel = {}));\nvar LifecycleStage;\n(function(LifecycleStage) {\n    LifecycleStage[\"DRAFT\"] = \"draft\";\n    LifecycleStage[\"ACTIVE\"] = \"active\";\n    LifecycleStage[\"DEPRECATED\"] = \"deprecated\";\n    LifecycleStage[\"RETIRED\"] = \"retired\";\n})(LifecycleStage || (LifecycleStage = {}));\nvar BusinessDataType;\n(function(BusinessDataType) {\n    BusinessDataType[\"FLASH_NEWS\"] = \"flash_news\";\n    BusinessDataType[\"NEWS_ARTICLE\"] = \"news_article\";\n    BusinessDataType[\"RESEARCH_REPORT\"] = \"research_report\";\n    BusinessDataType[\"ECONOMIC_DATA\"] = \"economic_data\";\n    BusinessDataType[\"COMPANY_ANNOUNCEMENT\"] = \"company_announcement\";\n    BusinessDataType[\"SOCIAL_SENTIMENT\"] = \"social_sentiment\";\n})(BusinessDataType || (BusinessDataType = {}));\nvar CollectionMethod;\n(function(CollectionMethod) {\n    CollectionMethod[\"API_JSON\"] = \"api_json\";\n    CollectionMethod[\"WEB_SCRAPING\"] = \"web_scraping\";\n    CollectionMethod[\"API_XML\"] = \"api_xml\";\n    CollectionMethod[\"API_RSS\"] = \"api_rss\";\n    CollectionMethod[\"WEB_DYNAMIC\"] = \"web_dynamic\";\n    CollectionMethod[\"FILE_UPLOAD\"] = \"file_upload\";\n})(CollectionMethod || (CollectionMethod = {}));\nvar ContentCategory;\n(function(ContentCategory) {\n    ContentCategory[\"FINANCIAL_NEWS\"] = \"financial_news\";\n    ContentCategory[\"OFFICIAL_DATA\"] = \"official_data\";\n    ContentCategory[\"MARKET_DATA\"] = \"market_data\";\n    ContentCategory[\"RESEARCH_REPORT\"] = \"research_report\";\n    ContentCategory[\"SOCIAL_MEDIA\"] = \"social_media\";\n    ContentCategory[\"REGULATORY_FILING\"] = \"regulatory_filing\";\n})(ContentCategory || (ContentCategory = {}));\nvar DataSourceStatus;\n(function(DataSourceStatus) {\n    DataSourceStatus[\"ACTIVE\"] = \"active\";\n    DataSourceStatus[\"INACTIVE\"] = \"inactive\";\n    DataSourceStatus[\"DISABLED\"] = \"disabled\";\n    DataSourceStatus[\"MAINTENANCE\"] = \"maintenance\";\n})(DataSourceStatus || (DataSourceStatus = {}));\nvar CrawlMode;\n(function(CrawlMode) {\n    CrawlMode[\"INTERVAL\"] = \"interval\";\n    CrawlMode[\"EVENT_DRIVEN\"] = \"event_driven\";\n    CrawlMode[\"HYBRID\"] = \"hybrid\";\n})(CrawlMode || (CrawlMode = {}));\nvar ModelType;\n(function(ModelType) {\n    ModelType[\"LLM\"] = \"llm\";\n    ModelType[\"CHAT\"] = \"chat\";\n    ModelType[\"CLASSIFICATION\"] = \"classification\";\n    ModelType[\"NER\"] = \"ner\";\n    ModelType[\"SENTIMENT\"] = \"sentiment\";\n    ModelType[\"EMBEDDING\"] = \"embedding\";\n    ModelType[\"RERANK\"] = \"rerank\";\n    ModelType[\"IMAGE\"] = \"image\";\n    ModelType[\"AUDIO\"] = \"audio\";\n})(ModelType || (ModelType = {}));\nvar ModelProvider;\n(function(ModelProvider) {\n    ModelProvider[\"DEEPSEEK\"] = \"deepseek\";\n    ModelProvider[\"QWEN\"] = \"qwen\";\n    ModelProvider[\"BAIDU\"] = \"baidu\";\n    ModelProvider[\"HUNYUAN\"] = \"hunyuan\";\n    ModelProvider[\"DOUBAO\"] = \"doubao\";\n})(ModelProvider || (ModelProvider = {}));\nvar ModelStatus;\n(function(ModelStatus) {\n    ModelStatus[\"ACTIVE\"] = \"active\";\n    ModelStatus[\"INACTIVE\"] = \"inactive\";\n    ModelStatus[\"TESTING\"] = \"testing\";\n    ModelStatus[\"DEPRECATED\"] = \"deprecated\";\n})(ModelStatus || (ModelStatus = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/authUtils.ts":
/*!********************************!*\
  !*** ./src/utils/authUtils.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handleLogout: () => (/* binding */ handleLogout),\n/* harmony export */   handleUnauthorizedError: () => (/* binding */ handleUnauthorizedError),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   setupTokenExpirationTimer: () => (/* binding */ setupTokenExpirationTimer)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/constants */ \"(ssr)/./src/constants/index.ts\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store */ \"(ssr)/./src/store/index.ts\");\n/* harmony import */ var _store_slices_authSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/slices/authSlice */ \"(ssr)/./src/store/slices/authSlice.ts\");\n/**\n * 认证工具函数\n * 处理token过期和自动登出相关逻辑\n */ \n\n\n/**\n * 设置token过期自动登出\n * @param expiresIn 过期时间（秒）\n */ const setupTokenExpirationTimer = (expiresIn)=>{\n    if (true) return;\n    // 清除之前可能存在的定时器\n    const previousTimerId = window.localStorage.getItem(\"token_expiration_timer_id\");\n    if (previousTimerId) {\n        clearTimeout(parseInt(previousTimerId));\n    }\n    // 设置新的定时器，提前30秒过期以确保安全\n    const safeExpirationTime = (expiresIn - 30) * 1000;\n    const timerId = setTimeout(()=>{\n        console.log(\"\\uD83D\\uDD11 Token已过期，自动登出\");\n        handleLogout();\n    }, safeExpirationTime);\n    // 存储定时器ID，以便在需要时清除\n    window.localStorage.setItem(\"token_expiration_timer_id\", timerId.toString());\n    // 记录过期时间\n    const expirationTime = Date.now() + expiresIn * 1000;\n    window.localStorage.setItem(\"token_expiration_time\", expirationTime.toString());\n    console.log(`⏱️ Token过期计时器已设置，将在 ${expiresIn} 秒后过期`);\n};\n/**\n * 检查token是否已过期\n * @returns 是否已过期\n */ const isTokenExpired = ()=>{\n    if (true) return true;\n    const expirationTimeStr = window.localStorage.getItem(\"token_expiration_time\");\n    if (!expirationTimeStr) return true;\n    const expirationTime = parseInt(expirationTimeStr);\n    return Date.now() >= expirationTime;\n};\n/**\n * 处理登出逻辑\n */ const handleLogout = ()=>{\n    // 清除定时器\n    const timerId = window.localStorage.getItem(\"token_expiration_timer_id\");\n    if (timerId) {\n        clearTimeout(parseInt(timerId));\n        window.localStorage.removeItem(\"token_expiration_timer_id\");\n    }\n    // 清除过期时间\n    window.localStorage.removeItem(\"token_expiration_time\");\n    // 触发登出action\n    _store__WEBPACK_IMPORTED_MODULE_1__.store.dispatch((0,_store_slices_authSlice__WEBPACK_IMPORTED_MODULE_2__.logout)());\n    // 重定向到登录页\n    window.location.href = _constants__WEBPACK_IMPORTED_MODULE_0__.ROUTES.LOGIN;\n};\n/**\n * 处理未授权错误\n */ const handleUnauthorizedError = ()=>{\n    console.log(\"\\uD83D\\uDEAB 未授权访问，自动登出\");\n    handleLogout();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/authUtils.ts\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e809f73da2ea\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmluc2lnaHQtY29uc29sZS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/ZDIxNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU4MDlmNzNkYTJlYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_StyledComponentsRegistry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/StyledComponentsRegistry */ \"(rsc)/./src/components/StyledComponentsRegistry/index.tsx\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Providers */ \"(rsc)/./src/components/Providers/index.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"FinSight Console\",\n    description: \"FinSight管理平台控制台\",\n    keywords: [\n        \"finsight\",\n        \"console\",\n        \"dashboard\",\n        \"admin\"\n    ],\n    authors: [\n        {\n            name: \"FinSight Team\"\n        }\n    ]\n};\n/**\n * 视口配置\n */ const viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    minimumScale: 1,\n    maximumScale: 5\n};\n/**\n * 根布局组件\n * 提供全局样式、主题配置和状态管理\n */ function RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StyledComponentsRegistry__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/app/layout.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/app/layout.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/app/layout.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/guangyu/app_finsight_console/src/app/layout.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Providers/index.tsx":
/*!********************************************!*\
  !*** ./src/components/Providers/index.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/Providers/index.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/StyledComponentsRegistry/index.tsx":
/*!***********************************************************!*\
  !*** ./src/components/StyledComponentsRegistry/index.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/workspace/guangyu/app_finsight_console/src/components/StyledComponentsRegistry/index.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/antd","vendor-chunks/next","vendor-chunks/@ant-design","vendor-chunks/@reduxjs","vendor-chunks/rc-util","vendor-chunks/react-redux","vendor-chunks/rc-motion","vendor-chunks/redux","vendor-chunks/styled-components","vendor-chunks/@babel","vendor-chunks/rc-pagination","vendor-chunks/@emotion","vendor-chunks/immer","vendor-chunks/stylis","vendor-chunks/reselect","vendor-chunks/use-sync-external-store","vendor-chunks/react-is","vendor-chunks/hoist-non-react-statics","vendor-chunks/rc-picker","vendor-chunks/@swc","vendor-chunks/classnames","vendor-chunks/redux-thunk","vendor-chunks/shallowequal"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fhenryhuang%2Fworkspace%2Fguangyu%2Fapp_finsight_console&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();