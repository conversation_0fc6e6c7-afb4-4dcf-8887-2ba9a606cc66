# 标签分类系统迁移文档

## 概述

根据后端接口更新，前端标签管理系统已从原有的 `tag_type` 和 `tag_categories` 架构迁移到新的 `classification` 系统。

## 主要变更

### 1. API类型定义更新 (`src/types/api.ts`)

#### 删除的类型
- `TagTypeCreate`, `TagTypeUpdate`, `TagTypeResponse`
- `TagTypeListResponse`, `GetTagTypesParams`
- `TagCategoryCreate`, `TagCategoryUpdate`, `TagCategoryResponse`
- `TagCategoryListResponse`, `GetTagCategoriesParams`

#### 新增的类型
- `TagClassificationCreate`, `TagClassificationUpdate`, `TagClassificationResponse`
- `TagClassificationListResponse`, `GetTagClassificationsParams`
- `ClassificationDimensionCreate`, `ClassificationDimensionUpdate`, `ClassificationDimensionResponse`
- `ClassificationDimensionListResponse`, `GetClassificationDimensionsParams`
- `ClassificationValueCreate`, `ClassificationValueUpdate`, `ClassificationValueResponse`
- `ClassificationValueListResponse`, `GetClassificationValuesParams`

#### 更新的类型
- `TagCreate`: `tag_type_id`, `tag_category_id` → `classification_id`
- `TagUpdate`: `tag_type_id`, `tag_category_id` → `classification_id`
- `TagResponse`: `tag_type_id`, `tag_category_id` → `classification_id`
- `GetTagsParams`: `tag_type_id`, `tag_category_id` → `classification_id`

### 2. API服务更新 (`src/services/tagsAPI.ts`)

#### 删除的方法
- `getTagTypes()`, `getTagTypeDetail()`, `createTagType()`, `updateTagType()`, `deleteTagType()`
- `getTagCategories()`, `getTagCategoryDetail()`, `createTagCategory()`, `updateTagCategory()`, `deleteTagCategory()`

#### 新增的方法
- `getTagClassifications()`, `getTagClassificationDetail()`, `createTagClassification()`, `updateTagClassification()`, `deleteTagClassification()`
- `getClassificationDimensions()`, `getClassificationDimensionDetail()`, `createClassificationDimension()`, `updateClassificationDimension()`, `deleteClassificationDimension()`
- `getClassificationValues()`, `getClassificationValueDetail()`, `createClassificationValue()`, `updateClassificationValue()`, `deleteClassificationValue()`

### 3. 组件更新

#### 删除的组件
- `TagTypesTable`
- `TagCategoriesTable`
- `TagTypeModal`
- `TagCategoryModal`

#### 新增的组件
- `TagClassificationsTable` - 标签分类表格
- `ClassificationDimensionsTable` - 分类维度表格
- `ClassificationValuesTable` - 分类值表格
- `TagClassificationModal` - 标签分类模态框
- `ClassificationDimensionModal` - 分类维度模态框
- `ClassificationValueModal` - 分类值模态框

#### 更新的组件
- `TagsTable`: 更新props从 `tagTypes`, `tagCategories` 到 `tagClassifications`
- `TagModal`: 暂时保持兼容，传入空数组

### 4. 主页面更新 (`src/app/admin/tags/page.tsx`)

#### Tab结构变更
- 删除: "标签类型", "标签分类"
- 新增: "标签分类", "分类维度", "分类值"
- 保留: "标签"

#### 状态管理更新
- 删除: `tagTypes`, `tagCategories` 相关状态
- 新增: `tagClassifications`, `classificationDimensions`, `classificationValues` 相关状态

### 5. 权限系统更新 (`src/constants/index.ts`)

#### TAG权限更新
```typescript
TAG: {
  CREATE: 'tag.tag.create',
  READ: 'tag.tag.read',
  UPDATE: 'tag.tag.update',
  DELETE: 'tag.tag.delete',
  LIST_READ: 'tag.list.read',
  CLASSIFICATION_CREATE: 'tag.classification.create',
  CLASSIFICATION_READ: 'tag.classification.read',
  CLASSIFICATION_UPDATE: 'tag.classification.update',
  CLASSIFICATION_DELETE: 'tag.classification.delete',
  ANALYTICS_READ: 'tag.analytics.read'
}
```

#### 新增CLASSIFICATION权限
```typescript
CLASSIFICATION: {
  DIMENSION_CREATE: 'classification.dimension.create',
  DIMENSION_READ: 'classification.dimension.read',
  DIMENSION_UPDATE: 'classification.dimension.update',
  DIMENSION_DELETE: 'classification.dimension.delete',
  VALUE_CREATE: 'classification.value.create',
  VALUE_READ: 'classification.value.read',
  VALUE_UPDATE: 'classification.value.update',
  VALUE_DELETE: 'classification.value.delete',
  ANALYTICS_READ: 'classification.analytics.read'
}
```

## 新架构说明

### 分类维度 (Classification Dimensions)
- 定义分类的维度，如"行业"、"地区"等
- 每个维度可以包含多个分类值

### 分类值 (Classification Values)
- 属于某个维度的具体分类值
- 支持层级结构（父子关系）
- 可以关联到标签分类

### 标签分类 (Tag Classifications)
- 标签的分类体系
- 支持层级结构
- 可以关联到具体的标签

### 标签 (Tags)
- 通过 `classification_id` 关联到标签分类
- 不再使用 `tag_type_id` 和 `tag_category_id`

## 测试

已创建基础测试文件 `src/components/tags/__tests__/ClassificationComponents.test.tsx`，包含：
- ClassificationDimensionsTable 测试
- ClassificationValuesTable 测试
- TagClassificationsTable 测试

## 部署注意事项

1. 确保后端API已更新到新的classification系统
2. 数据库迁移需要将现有的tag_type和tag_categories数据迁移到新的classification表
3. 权限配置需要更新，添加新的classification相关权限

## 兼容性

- 前端代码已完全迁移到新系统
- TagModal组件暂时保持向后兼容，但建议后续更新
- 所有废弃的组件和API调用已删除

## 后续工作

1. 更新TagModal组件以完全支持新的classification系统
2. 添加更完整的测试覆盖
3. 更新用户文档和操作指南
4. 性能优化和用户体验改进
