```bash
{
  "paths": {
    "/api/v1/admin/tags/classifications": {
      "get": {
        "tags": [
          "标签分类-B端"
        ],
        "summary": "获取标签分类列表",
        "description": "获取标签分类分页列表 (需要标签分类读取权限)",
        "operationId": "get_tag_classifications_api_v1_admin_tags_classifications_get",
        "security": [
          {
            "HTTPBearer": []
          }
        ],
        "parameters": [
          {
            "name": "page",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "minimum": 1,
              "description": "页码",
              "default": 1,
              "title": "Page"
            },
            "description": "页码"
          },
          {
            "name": "size",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "maximum": 100,
              "minimum": 1,
              "description": "每页数量",
              "default": 20,
              "title": "Size"
            },
            "description": "每页数量"
          },
          {
            "name": "classification_type",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "null"
                }
              ],
              "description": "分类类型过滤",
              "title": "Classification Type"
            },
            "description": "分类类型过滤"
          },
          {
            "name": "domain",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "null"
                }
              ],
              "description": "业务域过滤",
              "title": "Domain"
            },
            "description": "业务域过滤"
          },
          {
            "name": "parent_id",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "integer"
                },
                {
                  "type": "null"
                }
              ],
              "description": "父分类ID过滤",
              "title": "Parent Id"
            },
            "description": "父分类ID过滤"
          },
          {
            "name": "is_active",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "boolean"
                },
                {
                  "type": "null"
                }
              ],
              "description": "是否只获取活跃分类",
              "title": "Is Active"
            },
            "description": "是否只获取活跃分类"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/PageResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "标签分类-B端"
        ],
        "summary": "创建标签分类",
        "description": "创建新的标签分类 (需要标签分类创建权限)",
        "operationId": "create_tag_classification_api_v1_admin_tags_classifications_post",
        "security": [
          {
            "HTTPBearer": []
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/TagClassificationCreate"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/TagClassificationResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/admin/tags/classifications/{classification_id}": {
      "get": {
        "tags": [
          "标签分类-B端"
        ],
        "summary": "获取标签分类详情",
        "description": "根据ID获取标签分类详情 (需要标签分类读取权限)",
        "operationId": "get_tag_classification_api_v1_admin_tags_classifications__classification_id__get",
        "security": [
          {
            "HTTPBearer": []
          }
        ],
        "parameters": [
          {
            "name": "classification_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Classification Id"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/TagClassificationResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "标签分类-B端"
        ],
        "summary": "更新标签分类",
        "description": "更新标签分类信息 (需要标签分类更新权限)",
        "operationId": "update_tag_classification_api_v1_admin_tags_classifications__classification_id__put",
        "security": [
          {
            "HTTPBearer": []
          }
        ],
        "parameters": [
          {
            "name": "classification_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Classification Id"
            }
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/TagClassificationUpdate"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/TagClassificationResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      },
      "delete": {
        "tags": [
          "标签分类-B端"
        ],
        "summary": "删除标签分类",
        "description": "删除标签分类 (需要标签分类删除权限)",
        "operationId": "delete_tag_classification_api_v1_admin_tags_classifications__classification_id__delete",
        "security": [
          {
            "HTTPBearer": []
          }
        ],
        "parameters": [
          {
            "name": "classification_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Classification Id"
            }
          }
        ],
        "responses": {
          "204": {
            "description": "Successful Response"
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/admin/tags/classifications/tree": {
      "get": {
        "tags": [
          "标签分类-B端"
        ],
        "summary": "获取标签分类树",
        "description": "获取标签分类的树形结构 (需要标签分类读取权限)",
        "operationId": "get_tag_classifications_tree_api_v1_admin_tags_classifications_tree_get",
        "security": [
          {
            "HTTPBearer": []
          }
        ],
        "parameters": [
          {
            "name": "domain",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "null"
                }
              ],
              "description": "业务域过滤",
              "title": "Domain"
            },
            "description": "业务域过滤"
          },
          {
            "name": "is_active",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "boolean"
                },
                {
                  "type": "null"
                }
              ],
              "description": "是否只获取活跃分类",
              "title": "Is Active"
            },
            "description": "是否只获取活跃分类"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/TagClassificationResponse"
                  },
                  "title": "Response Get Tag Classifications Tree Api V1 Admin Tags Classifications Tree Get"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/admin/tags": {
      "get": {
        "tags": [
          "标签分类-B端"
        ],
        "summary": "获取标签列表",
        "description": "获取标签分页列表 (需要标签读取权限)",
        "operationId": "get_tags_api_v1_admin_tags_get",
        "security": [
          {
            "HTTPBearer": []
          }
        ],
        "parameters": [
          {
            "name": "page",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "minimum": 1,
              "description": "页码",
              "default": 1,
              "title": "Page"
            },
            "description": "页码"
          },
          {
            "name": "size",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "maximum": 100,
              "minimum": 1,
              "description": "每页数量",
              "default": 20,
              "title": "Size"
            },
            "description": "每页数量"
          },
          {
            "name": "search",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "null"
                }
              ],
              "description": "搜索关键词",
              "title": "Search"
            },
            "description": "搜索关键词"
          },
          {
            "name": "classification_id",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "integer"
                },
                {
                  "type": "null"
                }
              ],
              "description": "标签分类ID",
              "title": "Classification Id"
            },
            "description": "标签分类ID"
          },
          {
            "name": "parent_id",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "integer"
                },
                {
                  "type": "null"
                }
              ],
              "description": "父标签ID",
              "title": "Parent Id"
            },
            "description": "父标签ID"
          },
          {
            "name": "is_active",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "boolean"
                },
                {
                  "type": "null"
                }
              ],
              "description": "是否只获取活跃标签",
              "title": "Is Active"
            },
            "description": "是否只获取活跃标签"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/PageResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "标签分类-B端"
        ],
        "summary": "创建标签",
        "description": "创建新的标签 (需要标签创建权限)",
        "operationId": "create_tag_api_v1_admin_tags_post",
        "security": [
          {
            "HTTPBearer": []
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/TagCreate"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/TagResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/admin/tags/{tag_id}": {
      "get": {
        "tags": [
          "标签分类-B端"
        ],
        "summary": "获取标签详情",
        "description": "根据ID获取标签详情 (需要标签读取权限)",
        "operationId": "get_tag_api_v1_admin_tags__tag_id__get",
        "security": [
          {
            "HTTPBearer": []
          }
        ],
        "parameters": [
          {
            "name": "tag_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Tag Id"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/TagResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "标签分类-B端"
        ],
        "summary": "更新标签",
        "description": "更新标签信息 (需要标签更新权限)",
        "operationId": "update_tag_api_v1_admin_tags__tag_id__put",
        "security": [
          {
            "HTTPBearer": []
          }
        ],
        "parameters": [
          {
            "name": "tag_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Tag Id"
            }
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/TagUpdate"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/TagResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      },
      "delete": {
        "tags": [
          "标签分类-B端"
        ],
        "summary": "删除标签",
        "description": "删除标签 (需要标签删除权限)",
        "operationId": "delete_tag_api_v1_admin_tags__tag_id__delete",
        "security": [
          {
            "HTTPBearer": []
          }
        ],
        "parameters": [
          {
            "name": "tag_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Tag Id"
            }
          }
        ],
        "responses": {
          "204": {
            "description": "Successful Response"
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      }
    },
  },
  "components": {
    "schemas": {
      "TagClassificationCreate": {
        "properties": {
          "classification_code": {
            "type": "string",
            "maxLength": 50,
            "title": "Classification Code",
            "description": "分类代码"
          },
          "classification_name": {
            "type": "string",
            "maxLength": 100,
            "title": "Classification Name",
            "description": "分类名称"
          },
          "parent_id": {
            "anyOf": [
              {
                "type": "integer"
              },
              {
                "type": "null"
              }
            ],
            "title": "Parent Id",
            "description": "父分类ID"
          },
          "description": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Description",
            "description": "分类描述"
          },
          "icon": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 50
              },
              {
                "type": "null"
              }
            ],
            "title": "Icon",
            "description": "图标名称"
          },
          "color": {
            "anyOf": [
              {
                "type": "string",
                "pattern": "^#[0-9A-Fa-f]{6}$"
              },
              {
                "type": "null"
              }
            ],
            "title": "Color",
            "description": "颜色值"
          },
          "sort_order": {
            "type": "integer",
            "title": "Sort Order",
            "description": "排序权重",
            "default": 0
          }
        },
        "type": "object",
        "required": [
          "classification_code",
          "classification_name"
        ],
        "title": "TagClassificationCreate",
        "description": "标签分类创建请求"
      },
      "TagClassificationResponse": {
        "properties": {
          "id": {
            "type": "integer",
            "title": "Id"
          },
          "classification_code": {
            "type": "string",
            "title": "Classification Code"
          },
          "classification_name": {
            "type": "string",
            "title": "Classification Name"
          },
          "parent_id": {
            "anyOf": [
              {
                "type": "integer"
              },
              {
                "type": "null"
              }
            ],
            "title": "Parent Id"
          },
          "level": {
            "type": "integer",
            "title": "Level"
          },
          "path": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Path"
          },
          "description": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Description"
          },
          "icon": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Icon"
          },
          "color": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Color"
          },
          "sort_order": {
            "type": "integer",
            "title": "Sort Order"
          },
          "is_active": {
            "type": "boolean",
            "title": "Is Active"
          },
          "is_system": {
            "type": "boolean",
            "title": "Is System"
          },
          "created_at": {
            "type": "string",
            "format": "date-time",
            "title": "Created At"
          },
          "updated_at": {
            "type": "string",
            "format": "date-time",
            "title": "Updated At"
          },
          "children": {
            "anyOf": [
              {
                "items": {
                  "$ref": "#/components/schemas/TagClassificationResponse"
                },
                "type": "array"
              },
              {
                "type": "null"
              }
            ],
            "title": "Children"
          }
        },
        "type": "object",
        "required": [
          "id",
          "classification_code",
          "classification_name",
          "parent_id",
          "level",
          "path",
          "description",
          "icon",
          "color",
          "sort_order",
          "is_active",
          "is_system",
          "created_at",
          "updated_at"
        ],
        "title": "TagClassificationResponse",
        "description": "标签分类响应"
      },
      "TagClassificationUpdate": {
        "properties": {
          "classification_name": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 100
              },
              {
                "type": "null"
              }
            ],
            "title": "Classification Name",
            "description": "分类名称"
          },
          "parent_id": {
            "anyOf": [
              {
                "type": "integer"
              },
              {
                "type": "null"
              }
            ],
            "title": "Parent Id",
            "description": "父分类ID"
          },
          "description": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Description",
            "description": "分类描述"
          },
          "icon": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 50
              },
              {
                "type": "null"
              }
            ],
            "title": "Icon",
            "description": "图标名称"
          },
          "color": {
            "anyOf": [
              {
                "type": "string",
                "pattern": "^#[0-9A-Fa-f]{6}$"
              },
              {
                "type": "null"
              }
            ],
            "title": "Color",
            "description": "颜色值"
          },
          "sort_order": {
            "anyOf": [
              {
                "type": "integer"
              },
              {
                "type": "null"
              }
            ],
            "title": "Sort Order",
            "description": "排序权重"
          },
          "is_active": {
            "anyOf": [
              {
                "type": "boolean"
              },
              {
                "type": "null"
              }
            ],
            "title": "Is Active",
            "description": "是否启用"
          }
        },
        "type": "object",
        "title": "TagClassificationUpdate",
        "description": "标签分类更新请求"
      },
      
      "TagCreate": {
        "properties": {
          "tag_name": {
            "type": "string",
            "maxLength": 100,
            "title": "Tag Name",
            "description": "标签名称"
          },
          "tag_code": {
            "type": "string",
            "maxLength": 50,
            "title": "Tag Code",
            "description": "标签代码"
          },
          "tag_slug": {
            "type": "string",
            "maxLength": 50,
            "title": "Tag Slug",
            "description": "URL友好标识符"
          },
          "classification_id": {
            "type": "integer",
            "title": "Classification Id",
            "description": "标签分类ID"
          },
          "parent_id": {
            "anyOf": [
              {
                "type": "integer"
              },
              {
                "type": "null"
              }
            ],
            "title": "Parent Id",
            "description": "父标签ID"
          },
          "description": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Description",
            "description": "标签描述"
          },
          "color": {
            "anyOf": [
              {
                "type": "string",
                "pattern": "^#[0-9A-Fa-f]{6}$"
              },
              {
                "type": "null"
              }
            ],
            "title": "Color",
            "description": "标签颜色"
          },
          "icon": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 50
              },
              {
                "type": "null"
              }
            ],
            "title": "Icon",
            "description": "图标名称"
          },
          "synonyms": {
            "anyOf": [
              {
                "items": {
                  "type": "string"
                },
                "type": "array"
              },
              {
                "type": "null"
              }
            ],
            "title": "Synonyms",
            "description": "同义词列表",
            "default": []
          },
          "base_weight": {
            "type": "number",
            "maximum": 1,
            "minimum": 0,
            "title": "Base Weight",
            "description": "基础权重",
            "default": 1
          },
          "lifecycle_stage": {
            "allOf": [
              {
                "$ref": "#/components/schemas/LifecycleStage"
              }
            ],
            "description": "生命周期阶段",
            "default": "active"
          }
        },
        "type": "object",
        "required": [
          "tag_name",
          "tag_code",
          "tag_slug",
          "classification_id"
        ],
        "title": "TagCreate",
        "description": "标签创建请求"
      },
      "TagResponse": {
        "properties": {
          "id": {
            "type": "integer",
            "title": "Id"
          },
          "tag_name": {
            "type": "string",
            "title": "Tag Name"
          },
          "tag_code": {
            "type": "string",
            "title": "Tag Code"
          },
          "tag_slug": {
            "type": "string",
            "title": "Tag Slug"
          },
          "parent_id": {
            "anyOf": [
              {
                "type": "integer"
              },
              {
                "type": "null"
              }
            ],
            "title": "Parent Id"
          },
          "level": {
            "type": "integer",
            "title": "Level"
          },
          "path": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Path"
          },
          "classification_id": {
            "type": "integer",
            "title": "Classification Id"
          },
          "color": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Color"
          },
          "icon": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Icon"
          },
          "base_weight": {
            "type": "string",
            "title": "Base Weight"
          },
          "popularity_weight": {
            "type": "string",
            "title": "Popularity Weight"
          },
          "quality_weight": {
            "type": "string",
            "title": "Quality Weight"
          },
          "temporal_weight": {
            "type": "string",
            "title": "Temporal Weight"
          },
          "usage_count": {
            "type": "integer",
            "title": "Usage Count"
          },
          "daily_usage_count": {
            "type": "integer",
            "title": "Daily Usage Count"
          },
          "last_used_at": {
            "anyOf": [
              {
                "type": "string",
                "format": "date-time"
              },
              {
                "type": "null"
              }
            ],
            "title": "Last Used At"
          },
          "positive_feedback_count": {
            "type": "integer",
            "title": "Positive Feedback Count"
          },
          "negative_feedback_count": {
            "type": "integer",
            "title": "Negative Feedback Count"
          },
          "lifecycle_stage": {
            "type": "string",
            "title": "Lifecycle Stage"
          },
          "auto_retirement_date": {
            "anyOf": [
              {
                "type": "string",
                "format": "date"
              },
              {
                "type": "null"
              }
            ],
            "title": "Auto Retirement Date"
          },
          "description": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Description"
          },
          "synonyms": {
            "anyOf": [
              {
                "items": {
                  "type": "string"
                },
                "type": "array"
              },
              {
                "type": "null"
              }
            ],
            "title": "Synonyms"
          },
          "is_active": {
            "type": "boolean",
            "title": "Is Active"
          },
          "is_system": {
            "type": "boolean",
            "title": "Is System"
          },
          "created_at": {
            "type": "string",
            "format": "date-time",
            "title": "Created At"
          },
          "updated_at": {
            "type": "string",
            "format": "date-time",
            "title": "Updated At"
          },
          "classification": {
            "anyOf": [
              {
                "$ref": "#/components/schemas/TagClassificationResponse"
              },
              {
                "type": "null"
              }
            ]
          },
          "children": {
            "anyOf": [
              {
                "items": {
                  "$ref": "#/components/schemas/TagResponse"
                },
                "type": "array"
              },
              {
                "type": "null"
              }
            ],
            "title": "Children"
          }
        },
        "type": "object",
        "required": [
          "id",
          "tag_name",
          "tag_code",
          "tag_slug",
          "parent_id",
          "level",
          "path",
          "classification_id",
          "color",
          "icon",
          "base_weight",
          "popularity_weight",
          "quality_weight",
          "temporal_weight",
          "usage_count",
          "daily_usage_count",
          "last_used_at",
          "positive_feedback_count",
          "negative_feedback_count",
          "lifecycle_stage",
          "auto_retirement_date",
          "description",
          "synonyms",
          "is_active",
          "is_system",
          "created_at",
          "updated_at"
        ],
        "title": "TagResponse",
        "description": "标签响应"
      },
      "TagUpdate": {
        "properties": {
          "tag_name": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 100
              },
              {
                "type": "null"
              }
            ],
            "title": "Tag Name",
            "description": "标签名称"
          },
          "classification_id": {
            "anyOf": [
              {
                "type": "integer"
              },
              {
                "type": "null"
              }
            ],
            "title": "Classification Id",
            "description": "标签分类ID"
          },
          "parent_id": {
            "anyOf": [
              {
                "type": "integer"
              },
              {
                "type": "null"
              }
            ],
            "title": "Parent Id",
            "description": "父标签ID"
          },
          "description": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Description",
            "description": "标签描述"
          },
          "color": {
            "anyOf": [
              {
                "type": "string",
                "pattern": "^#[0-9A-Fa-f]{6}$"
              },
              {
                "type": "null"
              }
            ],
            "title": "Color",
            "description": "标签颜色"
          },
          "icon": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 50
              },
              {
                "type": "null"
              }
            ],
            "title": "Icon",
            "description": "图标名称"
          },
          "synonyms": {
            "anyOf": [
              {
                "items": {
                  "type": "string"
                },
                "type": "array"
              },
              {
                "type": "null"
              }
            ],
            "title": "Synonyms",
            "description": "同义词列表"
          },
          "base_weight": {
            "anyOf": [
              {
                "type": "number",
                "maximum": 1,
                "minimum": 0
              },
              {
                "type": "null"
              }
            ],
            "title": "Base Weight",
            "description": "基础权重"
          },
          "lifecycle_stage": {
            "anyOf": [
              {
                "$ref": "#/components/schemas/LifecycleStage"
              },
              {
                "type": "null"
              }
            ],
            "description": "生命周期阶段"
          },
          "is_active": {
            "anyOf": [
              {
                "type": "boolean"
              },
              {
                "type": "null"
              }
            ],
            "title": "Is Active",
            "description": "是否启用"
          }
        },
        "type": "object",
        "title": "TagUpdate",
        "description": "标签更新请求"
      },
      "LifecycleStage": {
        "type": "string",
        "enum": [
          "emerging",
          "active",
          "mature",
          "declining",
          "deprecated"
        ],
        "title": "LifecycleStage",
        "description": "标签生命周期阶段枚举"
      },
     
     
      


     "HTTPValidationError": {
        "properties": {
          "detail": {
            "items": {
              "$ref": "#/components/schemas/ValidationError"
            },
            "type": "array",
            "title": "Detail"
          }
        },
        "type": "object",
        "title": "HTTPValidationError"
      },
     "ValidationError": {
        "properties": {
          "loc": {
            "items": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "integer"
                }
              ]
            },
            "type": "array",
            "title": "Location"
          },
          "msg": {
            "type": "string",
            "title": "Message"
          },
          "type": {
            "type": "string",
            "title": "Error Type"
          }
        },
        "type": "object",
        "required": [
          "loc",
          "msg",
          "type"
        ],
        "title": "ValidationError"
      },
    "PageResponse": {
        "properties": {
          "items": {
            "items": {

            },
            "type": "array",
            "title": "Items"
          },
          "total": {
            "type": "integer",
            "title": "Total"
          },
          "page": {
            "type": "integer",
            "title": "Page"
          },
          "size": {
            "type": "integer",
            "title": "Size"
          },
          "pages": {
            "type": "integer",
            "title": "Pages"
          }
        },
        "type": "object",
        "required": [
          "items",
          "total",
          "page",
          "size",
          "pages"
        ],
        "title": "PageResponse",
        "description": "分页响应模型"
      },

}
```
```bash
接口权限对应表：
### 1. 标签分类管理接口 (TagClassification)

| 序号 | 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|------|
| 1 | GET | `/tags/classifications` | `tag.classification.read` | 获取标签分类列表 |
| 2 | GET | `/tags/classifications/{id}` | `tag.classification.read` | 获取标签分类详情 |
| 3 | POST | `/tags/classifications` | `tag.classification.create` | 创建标签分类 |
| 4 | PUT | `/tags/classifications/{id}` | `tag.classification.update` | 更新标签分类 |
| 5 | DELETE | `/tags/classifications/{id}` | `tag.classification.delete` | 删除标签分类 |
| 6 | GET | `/tags/classifications/tree` | `tag.classification.read` | 获取标签分类树 |

### 2. 标签管理接口 (Tag)

| 序号 | 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|------|
| 7 | GET | `/tags` | `tag.tag.read` | 获取标签列表 |
| 8 | GET | `/tags/{id}` | `tag.tag.read` | 获取标签详情 |
| 9 | POST | `/tags` | `tag.tag.create` | 创建标签 |
| 10 | PUT | `/tags/{id}` | `tag.tag.update` | 更新标签 |
| 11 | DELETE | `/tags/{id}` | `tag.tag.delete` | 删除标签 |
权限表：
 TAG: {
    CLASSIFICATION_READ: 'tag.classification.read',
    CLASSIFICATION_CREATE: 'tag.classification.create',
    CLASSIFICATION_UPDATE: 'tag.classification.update',
    CLASSIFICATION_DELETE: 'tag.classification.delete',
    UPDATE: 'tag.tag.update',
    DELETE: 'tag.tag.delete',
    READ: 'tag.tag.read',
    CREATE: 'tag.tag.create',
    ANALYTICS_READ: 'tag.analytics.read'
  },
  
  

```