/**
 * API相关类型定义
 */

export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  error?: string
}
// ==================== 认证相关 ====================

/**
 * 管理员发送验证码请求
 */
export interface AdminSendSmsRequest {
  phone: string
  purpose?: string
}

/**
 * 管理员发送验证码响应
 */
export interface AdminSendSmsResponse {
  message: string
}

/**
 * 管理员登录请求
 */
export interface AdminLoginRequest {
  phone: string
  verification_code: string
}

/**
 * 用户类型枚举
 */
export enum UserType {
  NOVICE = 1,    // 小白型
  ADVANCED = 2,  // 进阶型
  ANXIOUS = 3    // 焦虑型
}

/**
 * 风险等级枚举
 */
export enum RiskLevel {
  CONSERVATIVE = 1,          // 保守型
  MODERATE_CONSERVATIVE = 2, // 稳健偏保守型
  MODERATE = 3,              // 稳健型
  MODERATE_AGGRESSIVE = 4,   // 稳健偏进取型
  AGGRESSIVE = 5             // 进取型
}

/**
 * 金融知识水平等级枚举
 */
export enum KnowledgeLevel {
  BASIC = 1,        // 基础水平：了解基础金融术语和产品
  INTERMEDIATE = 2, // 中级水平：理解投资风险、回报和基本财务规划
  ADVANCED = 3,     // 高级水平：掌握复杂金融工具、市场分析和税务规划
  EXPERT = 4,       // 专家水平：精通金融衍生品、投资组合优化和风险管理
  PROFESSIONAL = 5  // 专业水平：具有金融行业资深经验或专业认证
}

/**
 * 权限信息
 */
export interface PermissionInfo {
  id: number
  code: string
  name: string
  description?: string
  module: string
  created_at: string
}

/**
 * 角色信息
 */
export interface RoleInfo {
  id: number
  name: string
  display_name: string
  description?: string
  created_at: string
}

/**
 * 管理员用户响应
 */
export interface AdminUserResponse {
  id: number
  phone: string
  username?: string | null
  email?: string | null
  user_type: UserType
  risk_level: RiskLevel
  knowledge_level: number
  is_active: boolean
  is_verified: boolean
  is_admin: boolean
  first_login_at?: string | null
  last_login_at?: string | null
  created_at: string
  updated_at: string
  roles?: RoleInfo[]
  permissions?: PermissionInfo[]
}

/**
 * 管理员登录响应
 */
export interface AdminLoginResponse {
  access_token: string
  token_type: string
  expires_in: number
  user: AdminUserResponse
}

// ==================== 用户管理相关 ====================

/**
 * 用户信息
 */
export interface User {
  id: number
  phone: string
  username?: string | null
  email?: string | null
  user_type: UserType
  risk_level: RiskLevel
  knowledge_level: number
  is_active: boolean
  is_verified: boolean
  is_admin: boolean
  first_login_at?: string | null
  last_login_at?: string | null
  created_at: string
  updated_at: string
  roles?: RoleInfo[]
  permissions?: PermissionInfo[]
}

/**
 * 用户创建请求
 */
export interface UserCreate {
  phone: string
  username?: string
  email?: string
  user_type: UserType
  risk_level?: RiskLevel
  knowledge_level?: number
  role_ids?: number[]
}

/**
 * 用户更新请求
 */
export interface UserUpdate {
  username?: string
  email?: string
  user_type?: UserType
  risk_level?: RiskLevel
  knowledge_level?: number
  role_ids?: number[]
}

/**
 * 用户状态更新请求
 */
export interface UserStatusUpdate {
  is_active: boolean
}

/**
 * 用户响应
 */
export interface UserResponse extends User {}

/**
 * 用户列表查询参数
 */
export interface GetUsersParams {
  page?: number
  size?: number
  user_type?: number
  is_active?: boolean
  is_verified?: boolean
  search?: string
}

/**
 * 用户列表响应
 */
export interface UserListResponse {
  items?: User[]
  users?: User[]
  total: number
  page: number
  size: number
  pages: number
}

/**
 * 用户详情响应
 */
export interface UserDetailResponse extends User {}

/**
 * 创建用户请求
 */
export interface CreateUserRequest extends UserCreate {}

/**
 * 更新用户请求
 */
export interface UpdateUserRequest extends UserUpdate {}

// ==================== 权限相关 ====================

/**
 * 权限信息（兼容旧接口）
 */
export interface Permission extends PermissionInfo {}

/**
 * 角色信息（兼容旧接口）
 */
export interface Role extends RoleInfo {
  permissions?: Permission[]
}

/**
 * 用户权限响应
 */
export interface UserPermissionsResponse {
  permissions: PermissionInfo[]
  roles: RoleInfo[]
  permission_codes: string[]
}

/**
 * 用户角色分配请求
 */
export interface UserRoleAssign {
  role_ids: number[]
}

/**
 * 批量角色分配请求
 */
export interface BatchRoleAssignRequest {
  user_ids: number[]
  role_id: number
}

/**
 * 操作结果
 */
export interface OperationResult {
  success: boolean
  message: string
}

// ==================== 分页相关 ====================

/**
 * 分页参数
 */
export interface PaginationParams {
  page: number
  size: number
}

/**
 * 分页响应
 */
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// ==================== 错误处理 ====================

/**
 * HTTP验证错误
 */
export interface HTTPValidationError {
  detail: Array<{
    loc: Array<string | number>
    msg: string
    type: string
  }>
}

/**
 * API错误响应
 */
export interface ApiErrorResponse {
  detail: string
  error_code?: string
}

// ==================== 权限管理相关 ====================

/**
 * 权限创建请求
 */
export interface PermissionCreate {
  code: string
  name: string
  description?: string
  module: string
  resource: string
  action: string
}

/**
 * 权限更新请求
 */
export interface PermissionUpdate {
  code?: string
  name?: string
  description?: string
  module?: string
  resource?: string
  action?: string
}

/**
 * 权限响应
 */
export interface PermissionResponse {
  id: number
  code: string
  name: string
  description?: string
  module: string
  resource: string
  action: string
  created_at: string
  updated_at: string
}

/**
 * 角色创建请求
 */
export interface RoleCreate {
  name: string
  description?: string
  parent_id?: number
  permission_ids?: number[]
}

/**
 * 角色更新请求
 */
export interface RoleUpdate {
  name?: string
  description?: string
  parent_id?: number
}

/**
 * 角色响应
 */
export interface RoleResponse {
  id: number
  name: string
  description?: string
  parent_id?: number
  is_system: boolean
  created_at: string
  updated_at: string
  permissions: PermissionResponse[]
  children: RoleResponse[]
}

/**
 * 角色权限分配请求
 */
export interface RolePermissionAssign {
  role_id: number
  permission_ids: number[]
}

/**
 * 用户权限列表响应
 */
export interface UserPermissionsListResponse {
  user_id: number
  roles: RoleResponse[]
  permissions: PermissionResponse[]
  permission_codes: string[]
}

/**
 * 批量角色分配请求
 */
export interface BatchRoleAssignRequest {
  user_ids: number[]
  role_id: number
}

/**
 * 操作结果响应
 */
export interface OperationResultResponse {
  success: boolean
  message: string
  affected_count?: number
}

/**
 * 权限查询参数
 */
export interface GetPermissionsParams {
  module?: string
}

/**
 * 角色权限查询参数
 */
export interface GetRolePermissionsParams {
  include_inherited?: boolean
}

// ==================== 标签分类管理相关 ====================

/**
 * 生命周期阶段枚举
 */
export enum LifecycleStage {
  DRAFT = 'draft',
  ACTIVE = 'active',
  DEPRECATED = 'deprecated',
  RETIRED = 'retired'
}

/**
 * 标签分类创建请求
 */
export interface TagClassificationCreate {
  classification_code: string
  classification_name: string
  parent_id?: number
  description?: string
  icon?: string
  color?: string
  sort_order?: number
}

/**
 * 标签分类更新请求
 */
export interface TagClassificationUpdate {
  classification_name?: string
  parent_id?: number
  description?: string
  icon?: string
  color?: string
  sort_order?: number
  is_active?: boolean
}

/**
 * 标签分类响应
 */
export interface TagClassificationResponse {
  id: number
  classification_code: string
  classification_name: string
  parent_id?: number
  level: number
  path?: string
  description?: string
  icon?: string
  color?: string
  sort_order: number
  is_active: boolean
  is_system: boolean
  created_at: string
  updated_at: string
  children?: TagClassificationResponse[]
}

/**
 * 标签创建请求
 */
export interface TagCreate {
  tag_name: string
  tag_code: string
  tag_slug: string
  classification_id: number
  parent_id?: number
  description?: string
  color?: string
  icon?: string
  synonyms?: string[]
  base_weight?: number
  lifecycle_stage?: LifecycleStage
  is_active?: boolean
}

/**
 * 标签更新请求
 */
export interface TagUpdate {
  tag_name?: string
  tag_code?: string
  tag_slug?: string
  classification_id?: number
  parent_id?: number
  description?: string
  color?: string
  icon?: string
  synonyms?: string[]
  base_weight?: number
  lifecycle_stage?: LifecycleStage
  is_active?: boolean
}

/**
 * 标签响应
 */
export interface TagResponse {
  id: number
  tag_name: string
  tag_code: string
  tag_slug: string
  parent_id?: number
  level: number
  path?: string
  classification_id: number
  color?: string
  icon?: string
  base_weight: string
  popularity_weight: string
  quality_weight: string
  temporal_weight: string
  usage_count: number
  daily_usage_count: number
  last_used_at?: string
  positive_feedback_count: number
  negative_feedback_count: number
  lifecycle_stage: string
  auto_retirement_date?: string
  description?: string
  synonyms?: string[]
  is_active: boolean
  is_system: boolean
  created_at: string
  updated_at: string
  classification?: TagClassificationResponse
  children?: TagResponse[]
}

/**
 * 分类维度创建请求
 */
export interface ClassificationDimensionCreate {
  dimension_name: string
  display_name: string
  description?: string
  sort_order?: number
}

/**
 * 分类维度更新请求
 */
export interface ClassificationDimensionUpdate {
  display_name?: string
  description?: string
  sort_order?: number
  is_active?: boolean
}

/**
 * 分类维度响应
 */
export interface ClassificationDimensionResponse {
  id: number
  dimension_name: string
  display_name: string
  description?: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
  values?: ClassificationValueResponse[]
}

/**
 * 分类值创建请求
 */
export interface ClassificationValueCreate {
  dimension_id: number
  value_code: string
  display_name: string
  description?: string
  parent_id?: number
  sort_order?: number
}

/**
 * 分类值更新请求
 */
export interface ClassificationValueUpdate {
  display_name?: string
  description?: string
  parent_id?: number
  sort_order?: number
  is_active?: boolean
}

/**
 * 分类值响应
 */
export interface ClassificationValueResponse {
  id: number
  dimension_id: number
  value_code: string
  display_name: string
  description?: string
  parent_id?: number
  level: number
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
  children?: ClassificationValueResponse[]
}

/**
 * 标签查询参数
 */
export interface GetTagsParams {
  page?: number
  size?: number
  classification_id?: number
  lifecycle_stage?: LifecycleStage
  is_active?: boolean
}

/**
 * 标签列表响应
 */
export interface TagListResponse {
  items: TagResponse[]
  total: number
  page: number
  size: number
  pages: number
}

/**
 * 标签分类查询参数
 */
export interface GetTagClassificationsParams {
  page?: number
  size?: number
  parent_id?: number
  level?: number
  is_active?: boolean
}

/**
 * 标签分类列表响应
 */
export interface TagClassificationListResponse {
  items: TagClassificationResponse[]
  total: number
  page: number
  size: number
  pages: number
}

/**
 * 分类维度创建请求
 */
export interface ClassificationDimensionCreate {
  dimension_name: string
  display_name: string
  description?: string
  sort_order?: number
}

/**
 * 分类维度更新请求
 */
export interface ClassificationDimensionUpdate {
  display_name?: string
  description?: string
  sort_order?: number
  is_active?: boolean
}

/**
 * 分类维度响应
 */
export interface ClassificationDimensionResponse {
  id: number
  dimension_name: string
  display_name: string
  description?: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
}

/**
 * 分类维度列表响应
 */
export interface ClassificationDimensionListResponse {
  items: ClassificationDimensionResponse[]
  total: number
  page: number
  size: number
  pages: number
}

/**
 * 分类维度查询参数
 */
export interface GetClassificationDimensionsParams {
  page?: number
  size?: number
  is_active?: boolean
}

/**
 * 分类值创建请求
 */
export interface ClassificationValueCreate {
  dimension_id: number
  value_code: string
  display_name: string
  parent_id?: number
  description?: string
  sort_order?: number
}

/**
 * 分类值更新请求
 */
export interface ClassificationValueUpdate {
  display_name?: string
  parent_id?: number
  description?: string
  sort_order?: number
  is_active?: boolean
}

/**
 * 分类值响应
 */
export interface ClassificationValueResponse {
  id: number
  dimension_id: number
  value_code: string
  display_name: string
  parent_id?: number
  level: number
  path?: string
  description?: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
  children?: ClassificationValueResponse[]
}

/**
 * 分类值列表响应
 */
export interface ClassificationValueListResponse {
  items: ClassificationValueResponse[]
  total: number
  page: number
  size: number
  pages: number
}

/**
 * 分类值查询参数
 */
export interface GetClassificationValuesParams {
  page?: number
  size?: number
  dimension_id?: number
  parent_id?: number
  level?: number
  is_active?: boolean
}

/**
 * 分类维度查询参数
 */
export interface GetClassificationDimensionsParams {
  is_active?: boolean
}

/**
 * 分类维度列表响应
 */
export interface ClassificationDimensionListResponse {
  items: ClassificationDimensionResponse[]
  total: number
  page: number
  size: number
  pages: number
}

/**
 * 分类值查询参数
 */
export interface GetClassificationValuesParams {
  page?: number
  size?: number
  dimension_id?: number
  parent_id?: number
  level?: number
  is_active?: boolean
  search?: string
}

/**
 * 分类值列表响应
 */
export interface ClassificationValueListResponse {
  items: ClassificationValueResponse[]
  total: number
  page: number
  size: number
  pages: number
}

// ==================== 数据采集相关 ====================

/**
 * 数据采集模块类型定义
 */

// 业务数据类型枚举
export enum BusinessDataType {
  FLASH_NEWS = 'flash_news',
  NEWS_ARTICLE = 'news_article',
  RESEARCH_REPORT = 'research_report',
  ECONOMIC_DATA = 'economic_data',
  COMPANY_ANNOUNCEMENT = 'company_announcement',
  SOCIAL_SENTIMENT = 'social_sentiment'
}

// 数据源相关类型
export enum CollectionMethod {
  API_JSON = 'api_json',
  WEB_SCRAPING = 'web_scraping',
  API_XML = 'api_xml',
  API_RSS = 'api_rss',
  WEB_DYNAMIC = 'web_dynamic',
  FILE_UPLOAD = 'file_upload'
}

export enum ContentCategory {
  FINANCIAL_NEWS = 'financial_news',
  OFFICIAL_DATA = 'official_data',
  MARKET_DATA = 'market_data',
  RESEARCH_REPORT = 'research_report',
  SOCIAL_MEDIA = 'social_media',
  REGULATORY_FILING = 'regulatory_filing'
}

export enum DataSourceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DISABLED = 'disabled',
  MAINTENANCE = 'maintenance'
}

export enum CrawlMode {
  INTERVAL = 'interval',
  EVENT_DRIVEN = 'event_driven',
  HYBRID = 'hybrid'
}

/**
 * 处理管道基础信息
 */
export interface ProcessingPipelineInfo {
  id: number
  pipeline_code: string
  pipeline_name: string
  description?: string | null
  is_active: boolean
}

/**
 * 数据源创建请求
 */
export interface DataSourceCreate {
  name: string
  collection_method: CollectionMethod
  content_category: ContentCategory
  business_data_type?: BusinessDataType
  base_url?: string | null
  description?: string | null
  crawl_mode?: CrawlMode
  crawl_interval?: number
  priority?: number
  max_concurrent_tasks?: number
  event_driven_config?: any | null
  supports_realtime?: boolean
  use_proxy?: boolean
  proxy_pool?: string | null
  request_delay_min?: number
  request_delay_max?: number
  max_consecutive_errors?: number
  created_by?: string | null
  tags?: string[] | null
}

/**
 * 数据源更新请求
 */
export interface DataSourceUpdate {
  name?: string | null
  collection_method?: CollectionMethod | null
  content_category?: ContentCategory | null
  business_data_type?: BusinessDataType | null
  base_url?: string | null
  description?: string | null
  crawl_mode?: CrawlMode | null
  crawl_interval?: number | null
  priority?: number | null
  max_concurrent_tasks?: number | null
  event_driven_config?: any | null
  supports_realtime?: boolean | null
  use_proxy?: boolean | null
  proxy_pool?: string | null
  request_delay_min?: number | null
  request_delay_max?: number | null
  status?: DataSourceStatus | null
  max_consecutive_errors?: number | null
  tags?: string[] | null
}

/**
 * 数据源响应
 */
export interface DataSourceResponse {
  id: number
  name: string
  collection_method: string
  content_category: string
  business_data_type: string
  base_url?: string | null
  description?: string | null
  crawl_mode: string
  crawl_interval: number
  priority: number
  max_concurrent_tasks: number
  event_driven_config?: any | null
  supports_realtime?: boolean | null
  use_proxy: boolean
  proxy_pool?: string | null
  request_delay_min: number
  request_delay_max: number
  status: string
  health_score: string
  last_health_check?: string | null
  last_crawl_time?: string | null
  last_success_time?: string | null
  next_crawl_time?: string | null
  error_count?: number | null
  consecutive_error_count?: number | null
  max_consecutive_errors?: number | null
  total_crawled_count?: number | null
  total_success_count?: number | null
  avg_response_time_ms?: number | null
  current_config_version?: number | null
  created_at?: string | null
  updated_at?: string | null
  created_by?: string | null
  tags: string[]
}

/**
 * 数据源统计响应
 */
export interface DataSourceStatsResponse {
  total_count: number
  active_count: number
  healthy_count: number
  avg_health_score: number
  avg_success_rate: number
}

/**
 * 数据源配置创建请求
 */
export interface DataSourceConfigCreate {
  source_id: number
  version: number
  selector_config?: any | null
  headers_config?: any | null
  cookies_config?: any | null
  request_params_config?: any | null
  javascript_config?: any | null
  anti_crawler_config?: any | null
  retry_config?: any | null
  proxy_config?: any | null
  is_active?: boolean
  change_reason?: string | null
  changed_by?: string | null
}

/**
 * 数据源配置更新请求
 */
export interface DataSourceConfigUpdate {
  selector_config?: any | null
  headers_config?: any | null
  cookies_config?: any | null
  request_params_config?: any | null
  javascript_config?: any | null
  anti_crawler_config?: any | null
  retry_config?: any | null
  proxy_config?: any | null
  is_active?: boolean | null
  is_validated?: boolean | null
  validation_result?: any | null
  change_reason?: string | null
  changed_by?: string | null
}

/**
 * 数据源配置响应
 */
export interface DataSourceConfigResponse {
  id: number
  source_id: number
  version: number
  selector_config: any
  headers_config: any
  cookies_config?: any | null
  request_params_config?: any | null
  javascript_config?: any | null
  anti_crawler_config: any
  retry_config: any
  proxy_config?: any | null
  is_active: boolean
  is_validated?: boolean | null
  validation_result?: any | null
  change_reason?: string | null
  changed_by?: string | null
  created_at?: string | null
}

/**
 * 原始数据记录创建请求
 */
export interface RawDataRecordCreate {
  task_id: number
  source_id: number
  source_url: string
  canonical_url?: string | null
  url_hash: string
  url_domain?: string | null
  content_hash?: string | null
  content_simhash?: number | null
  content_length?: number | null
  content_encoding?: string
  title?: string | null
  author?: string | null
  publish_time?: string | null
  crawl_time?: string | null
  mongodb_id?: string | null
  mongodb_collection?: string
  content_type?: string | null
  processing_status?: string
  processing_priority?: number
  quality_score?: number | null
  category?: string | null
  subcategory?: string | null
  language?: string | null
  view_count?: number
  like_count?: number
  comment_count?: number
  share_count?: number
  retention_policy?: string
  archive_after_days?: number
  delete_after_days?: number
  version?: number
  parent_record_id?: number | null
}

/**
 * 原始数据记录更新请求
 */
export interface RawDataRecordUpdate {
  processing_status?: string | null
  processing_priority?: number | null
  quality_score?: number | null
  category?: string | null
  subcategory?: string | null
  language?: string | null
  view_count?: number | null
  like_count?: number | null
  comment_count?: number | null
  share_count?: number | null
  retention_policy?: string | null
  archive_after_days?: number | null
  delete_after_days?: number | null
  is_archived?: boolean | null
  archived_at?: string | null
}

/**
 * 原始数据记录响应
 */
export interface RawDataRecordResponse {
  id: number
  task_id: number
  source_id: number
  source_url: string
  canonical_url?: string | null
  url_hash: string
  url_domain?: string | null
  content_hash?: string | null
  content_simhash?: number | null
  content_length?: number | null
  content_encoding: string
  title?: string | null
  author?: string | null
  publish_time?: string | null
  crawl_time: string
  mongodb_id?: string | null
  mongodb_collection: string
  content_type?: string | null
  processing_status: string
  processing_priority: number
  quality_score?: number | null
  category?: string | null
  subcategory?: string | null
  language?: string | null
  view_count: number
  like_count: number
  comment_count: number
  share_count: number
  retention_policy: string
  archive_after_days: number
  delete_after_days: number
  is_archived: boolean
  archived_at?: string | null
  version: number
  parent_record_id?: number | null
  created_at: string
  updated_at: string
}

/**
 * 批量状态更新请求
 */
export interface BatchStatusUpdateRequest {
  record_ids: number[]
  status: string
}

/**
 * 批量操作响应
 */
export interface BatchOperationResponse {
  success: boolean
  affected_count: number
  message: string
}

/**
 * 重复记录信息
 */
export interface DuplicateRecordInfo {
  id: number
  title?: string | null
  source_url: string
  created_at: string
}

/**
 * 重复记录组
 */
export interface DuplicateGroup {
  content_hash: string
  count: number
  records: DuplicateRecordInfo[]
}

/**
 * 重复记录响应
 */
export interface DuplicateRecordsResponse {
  duplicates: DuplicateGroup[]
  total_groups: number
}

// 分页响应类型
export interface DataSourceListResponse extends PaginatedResponse<DataSourceResponse> {}
export interface DataSourceConfigListResponse extends PaginatedResponse<DataSourceConfigResponse> {}
export interface RawDataRecordListResponse extends PaginatedResponse<RawDataRecordResponse> {}

/**
 * 数据源配置列表查询参数
 */
export interface GetDataSourceConfigsParams {
  skip?: number
  limit?: number
  source_id?: number | null
  is_active?: boolean | null
  is_validated?: boolean | null
}

/**
 * 原始数据记录列表查询参数
 */
export interface GetRawDataRecordsParams {
  skip?: number
  limit?: number
  source_id?: number | null
  task_id?: number | null
  processing_status?: string | null
  category?: string | null
  language?: string | null
  is_archived?: boolean | null
  start_date?: string | null
  end_date?: string | null
}

/**
 * 原始数据记录统计响应
 */
export interface RawDataRecordStatsResponse {
  total_records: number
  pending_records: number
  processed_records: number
  archived_records: number
  by_status: Record<string, number>
  by_category: Record<string, number>
  by_language: Record<string, number>
}

// ==================== AI模型管理相关 ====================

/**
 * 模型类型枚举
 */
export enum ModelType {
  LLM = 'llm',
  CHAT = 'chat',
  CLASSIFICATION = 'classification',
  NER = 'ner',
  SENTIMENT = 'sentiment',
  EMBEDDING = 'embedding',
  RERANK = 'rerank',
  IMAGE = 'image',
  AUDIO = 'audio'
}

/**
 * 模型提供商枚举
 */
export enum ModelProvider {
  DEEPSEEK = 'deepseek',
  QWEN = 'qwen',
  BAIDU = 'baidu',
  HUNYUAN = 'hunyuan',
  DOUBAO = 'doubao'
}

/**
 * 模型状态枚举
 */
export enum ModelStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  TESTING = 'testing',
  DEPRECATED = 'deprecated'
}

/**
 * 创建AI模型请求
 */
export interface AIModelCreate {
  model_name: string
  display_name?: string | null
  description?: string | null
  model_type: ModelType
  provider: ModelProvider
  model_version: string
  api_endpoint?: string | null
  api_key_name?: string | null
  model_params?: Record<string, any> | null
  max_tokens?: number
  temperature?: number | string
  top_p?: number | string
  frequency_penalty?: number | string
  presence_penalty?: number | string
  rate_limit_rpm?: number
  rate_limit_tpm?: number
  timeout_seconds?: number
  max_retries?: number
  cost_per_1k_input_tokens?: number | string
  cost_per_1k_output_tokens?: number | string
  cost_per_request?: number | string
  status?: ModelStatus
  is_default?: boolean
  priority?: number
}

/**
 * 更新AI模型请求
 */
export interface AIModelUpdate {
  display_name?: string | null
  description?: string | null
  model_version?: string | null
  api_endpoint?: string | null
  api_key_name?: string | null
  model_params?: Record<string, any> | null
  max_tokens?: number | null
  temperature?: number | string | null
  top_p?: number | string | null
  frequency_penalty?: number | string | null
  presence_penalty?: number | string | null
  rate_limit_rpm?: number | null
  rate_limit_tpm?: number | null
  timeout_seconds?: number | null
  max_retries?: number | null
  cost_per_1k_input_tokens?: number | string | null
  cost_per_1k_output_tokens?: number | string | null
  cost_per_request?: number | string | null
  status?: ModelStatus | null
  is_default?: boolean | null
  priority?: number | null
}

/**
 * AI模型响应
 */
export interface AIModelResponse {
  id: number
  model_name: string
  display_name?: string | null
  description?: string | null
  model_type: ModelType
  provider: ModelProvider
  model_version: string
  api_endpoint?: string | null
  api_key_name?: string | null
  model_params?: Record<string, any> | null
  max_tokens: number
  temperature: string
  top_p: string
  frequency_penalty: string
  presence_penalty: string
  rate_limit_rpm: number
  rate_limit_tpm: number
  timeout_seconds: number
  max_retries: number
  cost_per_1k_input_tokens: string
  cost_per_1k_output_tokens: string
  cost_per_request: string
  accuracy?: string | null
  latency_ms?: number | null
  availability?: string | null
  status: ModelStatus
  is_default: boolean
  priority: number
  total_requests: number
  success_requests: number
  failed_requests: number
  total_input_tokens: number
  total_output_tokens: number
  total_cost: string
  success_rate: number
  failure_rate: number
  average_cost_per_request: number
  created_at: string
  updated_at: string
  last_used_at?: string | null
}

/**
 * 模型列表响应
 */
export interface ModelListResponse {
  models: AIModelResponse[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

/**
 * 模型状态更新请求
 */
export interface ModelStatusUpdate {
  status: ModelStatus
}

/**
 * 模型默认设置更新请求
 */
export interface ModelDefaultUpdate {
  is_default: boolean
}

/**
 * 模型使用统计更新请求
 */
export interface ModelUsageStats {
  success: boolean
  input_tokens?: number
  output_tokens?: number
  latency_ms?: number
}

/**
 * 批量操作请求
 */
export interface ModelBatchOperation {
  model_ids: number[]
  operation: string
}

/**
 * 批量状态更新请求
 */
export interface ModelBatchStatusUpdate {
  model_ids: number[]
  status: ModelStatus
}

/**
 * 批量操作响应
 */
export interface ModelBatchOperationResponse {
  success_count: number
  failed_count: number
  success_ids: number[]
  failed_ids: number[]
  errors: string[]
}

/**
 * 系统模型信息响应
 */
export interface SystemModelInfo {
  total_models: number
  active_models: number
  default_models: Record<string, string>
  provider_distribution: Record<string, number>
  type_distribution: Record<string, number>
  total_requests_today: number
  total_cost_today: string
}

/**
 * 模型对比请求
 */
export interface ModelComparisonRequest {
  model_ids: number[]
  time_range?: string
}

/**
 * 模型对比响应
 */
export interface ModelComparisonResponse {
  models: Record<string, any>[]
  comparison_date_range: Record<string, string>
}

/**
 * 模型指标创建请求
 */
export interface ModelMetricsCreate {
  model_id: number
  metric_date: string
  time_window?: string
  total_requests?: number
  success_requests?: number
  failed_requests?: number
  avg_latency_ms?: number | null
  p95_latency_ms?: number | null
  p99_latency_ms?: number | null
  total_input_tokens?: number
  total_output_tokens?: number
  avg_input_tokens?: number | null
  avg_output_tokens?: number | null
  total_cost?: number | string
  avg_cost_per_request?: number | string | null
  accuracy_score?: number | string | null
  quality_score?: number | string | null
  user_satisfaction?: number | string | null
  timeout_errors?: number
  rate_limit_errors?: number
  api_errors?: number
  other_errors?: number
  extra_metadata?: Record<string, any> | null
}

/**
 * 模型指标响应
 */
export interface ModelMetricsResponse {
  id: number
  model_id: number
  metric_date: string
  time_window: string
  total_requests: number
  success_requests: number
  failed_requests: number
  success_rate: number
  error_rate: number
  avg_latency_ms?: number | null
  p95_latency_ms?: number | null
  p99_latency_ms?: number | null
  total_input_tokens: number
  total_output_tokens: number
  avg_input_tokens?: number | null
  avg_output_tokens?: number | null
  total_cost: string
  avg_cost_per_request?: string | null
  accuracy_score?: string | null
  quality_score?: string | null
  user_satisfaction?: string | null
  timeout_errors: number
  rate_limit_errors: number
  api_errors: number
  other_errors: number
  extra_metadata?: Record<string, any> | null
  created_at: string
}

/**
 * 模型性能响应
 */
export interface ModelPerformanceResponse {
  model_id: number
  model_name: string
  success_rate: number
  avg_latency_ms?: number | null
  total_cost: string
  total_requests: number
  metrics: ModelMetricsResponse[]
}

/**
 * 获取AI模型列表参数
 */
export interface GetAIModelsParams {
  model_type?: ModelType | null
  provider?: ModelProvider | null
  status?: ModelStatus | null
  is_default?: boolean | null
  search?: string | null
  page?: number
  page_size?: number
} 

// ==================== 数据处理管道相关 ====================

/**
 * 数据处理管道创建请求
 */
export interface DataProcessingPipelineCreate {
  pipeline_code: string
  version?: number
  pipeline_name: string
  description?: string | null
  business_data_type: string
  source_id?: number | null
  url_pattern?: string | null
  domain_pattern?: string | null
  content_type_pattern?: string | null
  content_pattern?: Record<string, any> | null
  min_content_length?: number | null
  max_content_length?: number | null
  quality_threshold?: number | string | null
  required_fields?: string[]
  field_mapping: Record<string, any>
  data_extraction_config?: Record<string, any>
  data_transformation_config?: Record<string, any>
  data_validation_config?: Record<string, any>
  data_enrichment_config?: Record<string, any>
  priority?: number | null
  execution_order?: number
  is_active?: boolean
  is_default?: boolean
  change_description?: string | null
  test_result?: Record<string, any> | null
  parent_version_id?: number | null
  effective_date_start?: string | null
  effective_date_end?: string | null
  created_by?: string | null
  updated_by?: string | null
}

/**
 * 数据处理管道更新请求
 */
export interface DataProcessingPipelineUpdate {
  pipeline_name?: string | null
  description?: string | null
  business_data_type?: string | null
  source_id?: number | null
  url_pattern?: string | null
  domain_pattern?: string | null
  content_type_pattern?: string | null
  content_pattern?: Record<string, any> | null
  min_content_length?: number | null
  max_content_length?: number | null
  quality_threshold?: number | string | null
  required_fields?: string[]
  field_mapping?: Record<string, any>
  data_extraction_config?: Record<string, any>
  data_transformation_config?: Record<string, any>
  data_validation_config?: Record<string, any>
  data_enrichment_config?: Record<string, any>
  priority?: number | null
  execution_order?: number | null
  is_active?: boolean | null
  is_default?: boolean | null
  change_description?: string | null
  test_result?: Record<string, any> | null
  parent_version_id?: number | null
  effective_date_start?: string | null
  effective_date_end?: string | null
  updated_by?: string | null
}

/**
 * 数据处理管道响应
 */
export interface DataProcessingPipelineResponse {
  id: number
  pipeline_code: string
  version: number
  pipeline_name: string
  description?: string | null
  business_data_type: string
  source_id?: number | null
  url_pattern?: string | null
  domain_pattern?: string | null
  content_type_pattern?: string | null
  content_pattern?: Record<string, any> | null
  min_content_length?: number | null
  max_content_length?: number | null
  quality_threshold?: string | null
  required_fields: string[]
  field_mapping: Record<string, any>
  data_extraction_config: Record<string, any>
  data_transformation_config: Record<string, any>
  data_validation_config: Record<string, any>
  data_enrichment_config: Record<string, any>
  priority: number
  execution_order: number
  is_active: boolean
  is_default: boolean
  change_description?: string | null
  test_result?: Record<string, any> | null
  parent_version_id?: number | null
  effective_date_start?: string | null
  effective_date_end?: string | null
  created_by?: string | null
  updated_by?: string | null
  created_at: string
  updated_at: string
}

/**
 * 数据处理管道列表响应
 */
export interface DataProcessingPipelineListResponse {
  items: DataProcessingPipelineResponse[]
  total: number
  page: number
  size: number
  pages: number
}

/**
 * 数据处理管道统计响应
 */
export interface DataProcessingPipelineStatsResponse {
  total_pipelines: number
  active_pipelines: number
  inactive_pipelines: number
  default_pipelines: number
  by_business_type: Record<string, number>
  by_priority: Record<string, number>
}

/**
 * 获取数据处理管道查询参数
 */
export interface GetDataProcessingPipelinesParams {
  skip?: number
  limit?: number
  page?: number
  size?: number
  pipeline_code?: string
  business_data_type?: string
  is_active?: boolean
  is_default?: boolean
  source_id?: number
  priority?: number
  search?: string
}

/**
 * 批量管道状态更新
 */
export interface BatchPipelineStatusUpdate {
  pipeline_ids: number[]
  is_active: boolean
}

/**
 * 批量管道优先级更新
 */
export interface BatchPipelinePriorityUpdate {
  updates: Array<{
    pipeline_id: number
    priority: number
  }>
}

// ==================== 快讯管理相关 ====================

/**
 * 内容分类信息
 */
export interface ContentClassificationInfo {
  /** 分类维度ID */
  dimension_id: number
  /** 维度名称 */
  dimension_name: string
  /** 维度显示名称 */
  dimension_display_name: string
  /** 分类值ID */
  value_id: number
  /** 分类值代码 */
  value_code: string
  /** 分类值显示名称 */
  value_display_name: string
  /** 置信度评分 */
  confidence_score: string
  /** 分类来源 */
  source: string
}

/**
 * 内容标签信息
 */
export interface ContentTagInfo {
  /** 标签ID */
  tag_id: number
  /** 标签名称 */
  tag_name: string
  /** 标签代码 */
  tag_code: string
  /** 标签URL标识符 */
  tag_slug: string
  /** 标签颜色 */
  color?: string | null
  /** 标签图标 */
  icon?: string | null
  /** 相关性评分 */
  relevance_score: string
  /** 置信度评分 */
  confidence_score: string
  /** 重要性评分 */
  importance_score: string
  /** 综合评分 */
  final_score: string
  /** 标签来源 */
  source: string
  /** 提及次数 */
  mention_count: number
}

/**
 * 快讯响应模型
 */
export interface FlashNewsResponse {
  /** 快讯唯一标识符 */
  id: number
  /** 快讯标题 */
  title: string
  /** 快讯内容 */
  content: string
  /** AI生成摘要 */
  summary?: string | null
  /** 发布时间 */
  publish_time: string
  /** 紧急度级别：1普通/2重要/3紧急 */
  urgency_level: number
  /** 重要性评分 */
  importance_score?: string | null
  /** 影响范围：domestic/international/global */
  impact_scope: string
  /** 新闻分类 */
  news_category?: string | null
  /** 状态：draft/published/updated/archived */
  status: string
  /** 是否突发新闻 */
  is_breaking: boolean
  /** 是否立即推送 */
  push_immediately: boolean
  /** 关联原始数据ID */
  raw_data_id: number
  /** 处理时间 */
  process_time: string
  /** 浏览次数 */
  view_count: number
  /** 分享次数 */
  share_count: number
  /** 关联标签列表 */
  tags: ContentTagInfo[]
  /** 分类信息列表 */
  classifications: ContentClassificationInfo[]
  /** 创建时间 */
  created_at: string
  /** 更新时间 */
  updated_at: string
}

/**
 * 快讯列表响应模型
 */
export interface FlashNewsListResponse {
  /** 快讯列表 */
  items: FlashNewsResponse[]
  /** 总数量 */
  total: number
  /** 跳过数量 */
  skip: number
  /** 限制数量 */
  limit: number
}

/**
 * 更新快讯请求模型
 */
export interface FlashNewsUpdate {
  /** 快讯标题 */
  title?: string | null
  /** 快讯内容 */
  content?: string | null
  /** AI生成摘要 */
  summary?: string | null
  /** 发布时间 */
  publish_time?: string | null
  /** 紧急度级别：1普通/2重要/3紧急 */
  urgency_level?: number | null
  /** 重要性评分 */
  importance_score?: string | null
  /** 影响范围：domestic/international/global */
  impact_scope?: string | null
  /** 新闻分类 */
  news_category?: string | null
  /** 状态：draft/published/updated/archived */
  status?: string | null
  /** 是否突发新闻 */
  is_breaking?: boolean | null
  /** 是否立即推送 */
  push_immediately?: boolean | null
}

/**
 * 获取快讯列表查询参数
 */
export interface GetFlashNewsParams {
  /** 跳过的记录数 */
  skip?: number
  /** 返回的记录数 */
  limit?: number
  /** 状态过滤 */
  status?: string | null
  /** 紧急度过滤 */
  urgency_level?: number | null
  /** 新闻分类过滤 */
  news_category?: string | null
  /** 是否突发新闻过滤 */
  is_breaking?: boolean | null
}

/**
 * 快讯统计响应模型
 */
export interface FlashNewsStatsResponse {
  /** 总快讯数 */
  total_news: number
  /** 已发布快讯数 */
  published_news: number
  /** 草稿快讯数 */
  draft_news: number
  /** 突发新闻数 */
  breaking_news: number
  /** 按紧急度分组统计 */
  by_urgency_level: Record<string, number>
  /** 按分类分组统计 */
  by_category: Record<string, number>
  /** 按状态分组统计 */
  by_status: Record<string, number>
}
