/**
 * 标签分类模态框组件
 */

'use client'

import React, { useEffect, useState } from 'react'
import { Modal, Form, Input, InputNumber, TreeSelect, ColorPicker, message } from 'antd'
import { 
  TagClassificationResponse, 
  TagClassificationCreate 
} from '@/types/api'

const { TextArea } = Input

interface TagClassificationModalProps {
  visible: boolean
  editingData?: TagClassificationResponse | null
  classifications: TagClassificationResponse[]
  onCancel: () => void
  onSubmit: (data: TagClassificationCreate) => Promise<void>
}

/**
 * 标签分类模态框组件
 */
export const TagClassificationModal: React.FC<TagClassificationModalProps> = ({
  visible,
  editingData,
  classifications,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const isEditing = !!editingData

  useEffect(() => {
    if (visible) {
      if (editingData) {
        form.setFieldsValue({
          classification_name: editingData.classification_name,
          parent_id: editingData.parent_id,
          description: editingData.description,
          icon: editingData.icon,
          color: editingData.color,
          sort_order: editingData.sort_order,
        })
      } else {
        form.resetFields()
        // 设置默认值
        form.setFieldsValue({
          sort_order: 0,
          color: '#1890ff',
        })
      }
    }
  }, [visible, editingData, form])

  /**
   * 生成分类代码
   */
  const generateClassificationCode = (classificationName: string): string => {
    return classificationName
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^\w\-_]/g, '')
  }

  /**
   * 构建父分类树数据
   */
  const buildParentTreeData = () => {
    const buildTree = (parentId: number | null = null): any[] => {
      return classifications
        .filter(classification => 
          classification.parent_id === parentId && 
          (!editingData || classification.id !== editingData.id)
        )
        .map(classification => ({
          title: classification.classification_name,
          value: classification.id,
          key: classification.id,
          children: buildTree(classification.id),
        }))
    }

    return buildTree()
  }

  /**
   * 提交表单
   */
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true)

      const classificationData: TagClassificationCreate = {
        classification_code: generateClassificationCode(values.classification_name),
        classification_name: values.classification_name,
        parent_id: values.parent_id || undefined,
        description: values.description || '',
        icon: values.icon || '',
        color: values.color || '#1890ff',
        sort_order: values.sort_order || 0,
      }

      await onSubmit(classificationData)
    } catch (error) {
      console.error('保存标签分类失败:', error)
      message.error('保存失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 处理取消
   */
  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title={isEditing ? '编辑标签分类' : '创建标签分类'}
      open={visible}
      onCancel={handleCancel}
      onOk={() => form.submit()}
      confirmLoading={loading}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        preserve={false}
      >
        <Form.Item
          name="classification_name"
          label="分类名称"
          rules={[
            { required: true, message: '请输入分类名称' },
            { min: 2, message: '分类名称至少2个字符' },
            { max: 100, message: '分类名称不能超过100个字符' },
          ]}
        >
          <Input placeholder="请输入分类名称" />
        </Form.Item>

        <Form.Item
          name="parent_id"
          label="父分类"
          help="选择父分类可以创建层级分类结构"
        >
          <TreeSelect
            placeholder="请选择父分类（可选）"
            allowClear
            treeData={buildParentTreeData()}
            treeDefaultExpandAll
          />
        </Form.Item>

        <Form.Item
          name="description"
          label="描述"
        >
          <TextArea 
            placeholder="请输入分类描述" 
            rows={3}
            maxLength={500}
            showCount
          />
        </Form.Item>

        <Form.Item
          name="icon"
          label="图标"
          help="支持Ant Design图标名称或图标字符"
        >
          <Input placeholder="如：TagOutlined" />
        </Form.Item>

        <Form.Item
          name="color"
          label="颜色"
        >
          <ColorPicker showText />
        </Form.Item>

        <Form.Item
          name="sort_order"
          label="排序"
          help="数字越小排序越靠前"
        >
          <InputNumber 
            min={0} 
            max={9999} 
            style={{ width: '100%' }} 
            placeholder="请输入排序值"
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default TagClassificationModal
