/**
 * 分类值表格组件
 */

'use client'

import React from 'react'
import { Table, Button, Space, Popconfirm, Tag, Tooltip } from 'antd'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import styled from 'styled-components'
import { ClassificationValueResponse, ClassificationDimensionResponse } from '@/types/api'

const StyledTable = styled(Table<ClassificationValueResponse>)`
  .ant-table-thead > tr > th {
    background: ${props => props.theme.colors.background.primary};
    border-bottom: 2px solid ${props => props.theme.colors.border.default};
    font-weight: 600;
  }
`

interface ClassificationValuesTableProps {
  values: ClassificationValueResponse[]
  dimensions: ClassificationDimensionResponse[]
  loading?: boolean
  onEdit: (value: ClassificationValueResponse) => void
  onDelete: (valueId: number) => void
}

export const ClassificationValuesTable: React.FC<ClassificationValuesTableProps> = ({
  values,
  dimensions,
  loading = false,
  onEdit,
  onDelete,
}) => {
  const getDimensionName = (dimensionId: number): string => {
    const dimension = dimensions.find(d => d.id === dimensionId)
    return dimension?.display_name || `维度${dimensionId}`
  }

  const columns: ColumnsType<ClassificationValueResponse> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '所属维度',
      dataIndex: 'dimension_id',
      key: 'dimension_id',
      width: 120,
      render: (dimensionId: number) => (
        <Tag color="purple">{getDimensionName(dimensionId)}</Tag>
      ),
    },
    {
      title: '分类值代码',
      dataIndex: 'value_code',
      key: 'value_code',
      width: 120,
      render: (code: string) => (
        <Tag color="blue">{code}</Tag>
      ),
    },
    {
      title: '显示名称',
      dataIndex: 'display_name',
      key: 'display_name',
      width: 150,
      render: (name: string, record: ClassificationValueResponse) => (
        <Space>
          <span style={{ marginLeft: record.level * 20 }}>{name}</span>
        </Space>
      ),
    },
    {
      title: '层级',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level: number) => (
        <Tag color="orange">L{level}</Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (description: string) => (
        <Tooltip title={description}>
          <span style={{ 
            display: 'block',
            maxWidth: '200px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {description || '-'}
          </span>
        </Tooltip>
      ),
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 80,
      render: (order: number) => (
        <Tag color="cyan">{order}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => onEdit(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确认删除"
              description="确定要删除这个分类值吗？"
              onConfirm={() => onDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  return (
    <StyledTable
      columns={columns}
      dataSource={Array.isArray(values) ? values : []}
      rowKey="id"
      loading={loading}
      pagination={{
        total: values?.length || 0,
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
      }}
      scroll={{ x: 1200 }}
      size="middle"
    />
  )
}

export default ClassificationValuesTable
