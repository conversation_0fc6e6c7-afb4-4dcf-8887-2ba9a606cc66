/**
 * 分类维度表格组件
 */

'use client'

import React from 'react'
import { Table, Button, Space, Popconfirm, Tag, Tooltip } from 'antd'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import styled from 'styled-components'
import { ClassificationDimensionResponse } from '@/types/api'

const StyledTable = styled(Table<ClassificationDimensionResponse>)`
  .ant-table-thead > tr > th {
    background: ${props => props.theme.colors.background.primary};
    border-bottom: 2px solid ${props => props.theme.colors.border.default};
    font-weight: 600;
  }
`

interface ClassificationDimensionsTableProps {
  dimensions: ClassificationDimensionResponse[]
  loading?: boolean
  onEdit: (dimension: ClassificationDimensionResponse) => void
  onDelete: (dimensionId: number) => void
}

export const ClassificationDimensionsTable: React.FC<ClassificationDimensionsTableProps> = ({
  dimensions,
  loading = false,
  onEdit,
  onDelete,
}) => {
  const columns: ColumnsType<ClassificationDimensionResponse> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '维度名称',
      dataIndex: 'dimension_name',
      key: 'dimension_name',
      width: 150,
      render: (name: string) => (
        <Tag color="blue">{name}</Tag>
      ),
    },
    {
      title: '显示名称',
      dataIndex: 'display_name',
      key: 'display_name',
      width: 150,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (description: string) => (
        <Tooltip title={description}>
          <span style={{ 
            display: 'block',
            maxWidth: '200px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {description || '-'}
          </span>
        </Tooltip>
      ),
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 80,
      render: (order: number) => (
        <Tag color="orange">{order}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => onEdit(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确认删除"
              description="确定要删除这个分类维度吗？"
              onConfirm={() => onDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  return (
    <StyledTable
      columns={columns}
      dataSource={Array.isArray(dimensions) ? dimensions : []}
      rowKey="id"
      loading={loading}
      pagination={{
        total: dimensions?.length || 0,
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
      }}
      scroll={{ x: 1000 }}
      size="middle"
    />
  )
}

export default ClassificationDimensionsTable
