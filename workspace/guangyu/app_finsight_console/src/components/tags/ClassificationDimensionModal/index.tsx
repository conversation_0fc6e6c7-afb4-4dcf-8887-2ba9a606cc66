/**
 * 分类维度模态框组件
 */

'use client'

import React, { useEffect, useState } from 'react'
import { Modal, Form, Input, InputNumber, message } from 'antd'
import { ClassificationDimensionResponse, ClassificationDimensionCreate } from '@/types/api'

const { TextArea } = Input

interface ClassificationDimensionModalProps {
  visible: boolean
  editingData?: ClassificationDimensionResponse | null
  onCancel: () => void
  onSubmit: (data: ClassificationDimensionCreate) => Promise<void>
}

/**
 * 分类维度模态框组件
 */
export const ClassificationDimensionModal: React.FC<ClassificationDimensionModalProps> = ({
  visible,
  editingData,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const isEditing = !!editingData

  useEffect(() => {
    if (visible) {
      if (editingData) {
        form.setFieldsValue({
          dimension_name: editingData.dimension_name,
          display_name: editingData.display_name,
          description: editingData.description,
          sort_order: editingData.sort_order,
        })
      } else {
        form.resetFields()
        // 设置默认值
        form.setFieldsValue({
          sort_order: 0,
        })
      }
    }
  }, [visible, editingData, form])

  /**
   * 生成维度代码
   */
  const generateDimensionCode = (displayName: string): string => {
    return displayName
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^\w\-_]/g, '')
  }

  /**
   * 提交表单
   */
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true)

      const dimensionData: ClassificationDimensionCreate = {
        dimension_name: generateDimensionCode(values.display_name),
        display_name: values.display_name,
        description: values.description || '',
        sort_order: values.sort_order || 0,
      }

      await onSubmit(dimensionData)
    } catch (error) {
      console.error('保存分类维度失败:', error)
      message.error('保存失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 处理取消
   */
  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title={isEditing ? '编辑分类维度' : '创建分类维度'}
      open={visible}
      onCancel={handleCancel}
      onOk={() => form.submit()}
      confirmLoading={loading}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        preserve={false}
      >
        <Form.Item
          name="display_name"
          label="显示名称"
          rules={[
            { required: true, message: '请输入显示名称' },
            { min: 2, message: '显示名称至少2个字符' },
            { max: 100, message: '显示名称不能超过100个字符' },
          ]}
        >
          <Input placeholder="请输入显示名称" />
        </Form.Item>

        <Form.Item
          name="description"
          label="描述"
        >
          <TextArea 
            placeholder="请输入维度描述" 
            rows={3}
            maxLength={500}
            showCount
          />
        </Form.Item>

        <Form.Item
          name="sort_order"
          label="排序"
          help="数字越小排序越靠前"
        >
          <InputNumber 
            min={0} 
            max={9999} 
            style={{ width: '100%' }} 
            placeholder="请输入排序值"
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default ClassificationDimensionModal
