/**
 * 标签分类表格组件
 */

'use client'

import React from 'react'
import { Table, Button, Space, Popconfirm, Tag, Tooltip } from 'antd'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import styled from 'styled-components'
import { TagClassificationResponse } from '@/types/api'

const StyledTable = styled(Table<TagClassificationResponse>)`
  .ant-table-thead > tr > th {
    background: ${props => props.theme.colors.background.primary};
    border-bottom: 2px solid ${props => props.theme.colors.border.default};
    font-weight: 600;
  }
`

interface TagClassificationsTableProps {
  classifications: TagClassificationResponse[]
  loading?: boolean
  onEdit: (classification: TagClassificationResponse) => void
  onDelete: (classificationId: number) => void
}

export const TagClassificationsTable: React.FC<TagClassificationsTableProps> = ({
  classifications,
  loading = false,
  onEdit,
  onDelete,
}) => {
  const columns: ColumnsType<TagClassificationResponse> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '分类代码',
      dataIndex: 'classification_code',
      key: 'classification_code',
      width: 120,
      render: (code: string) => (
        <Tag color="blue">{code}</Tag>
      ),
    },
    {
      title: '分类名称',
      dataIndex: 'classification_name',
      key: 'classification_name',
      width: 150,
      render: (name: string, record: TagClassificationResponse) => (
        <Space>
          {record.icon && (
            <span 
              style={{ 
                color: record.color || '#666',
                fontSize: '16px'
              }}
            >
              {record.icon}
            </span>
          )}
          <span style={{ marginLeft: record.level * 20 }}>{name}</span>
        </Space>
      ),
    },
    {
      title: '层级',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level: number) => (
        <Tag color="orange">L{level}</Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (description: string) => (
        <Tooltip title={description}>
          <span style={{ 
            display: 'block',
            maxWidth: '200px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {description || '-'}
          </span>
        </Tooltip>
      ),
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 80,
      render: (order: number) => (
        <Tag color="cyan">{order}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '系统',
      dataIndex: 'is_system',
      key: 'is_system',
      width: 80,
      render: (isSystem: boolean) => (
        <Tag color={isSystem ? 'purple' : 'default'}>
          {isSystem ? '系统' : '自定义'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => onEdit(record)}
              size="small"
              disabled={record.is_system}
            />
          </Tooltip>
          <Tooltip title={record.is_system ? '系统分类不能删除' : '删除'}>
            <Popconfirm
              title="确认删除"
              description="确定要删除这个标签分类吗？"
              onConfirm={() => onDelete(record.id)}
              okText="确定"
              cancelText="取消"
              disabled={record.is_system}
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
                size="small"
                disabled={record.is_system}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  return (
    <StyledTable
      columns={columns}
      dataSource={Array.isArray(classifications) ? classifications : []}
      rowKey="id"
      loading={loading}
      pagination={{
        total: classifications?.length || 0,
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
      }}
      scroll={{ x: 1200 }}
      size="middle"
    />
  )
}

export default TagClassificationsTable
