/**
 * Classification组件测试
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { ThemeProvider } from 'styled-components'
import { theme } from '@/styles/theme'
import { ClassificationDimensionsTable } from '../ClassificationDimensionsTable'
import { ClassificationValuesTable } from '../ClassificationValuesTable'
import { TagClassificationsTable } from '../TagClassificationsTable'
import { 
  ClassificationDimensionResponse, 
  ClassificationValueResponse,
  TagClassificationResponse 
} from '@/types/api'

// Mock数据
const mockDimensions: ClassificationDimensionResponse[] = [
  {
    id: 1,
    dimension_name: 'industry',
    display_name: '行业',
    description: '行业分类维度',
    sort_order: 1,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
]

const mockValues: ClassificationValueResponse[] = [
  {
    id: 1,
    dimension_id: 1,
    value_code: 'finance',
    display_name: '金融',
    level: 1,
    description: '金融行业',
    sort_order: 1,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
]

const mockClassifications: TagClassificationResponse[] = [
  {
    id: 1,
    classification_code: 'industry_finance',
    classification_name: '金融行业',
    level: 1,
    description: '金融行业分类',
    sort_order: 1,
    is_active: true,
    is_system: false,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
]

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  )
}

describe('Classification Components', () => {
  describe('ClassificationDimensionsTable', () => {
    it('应该正确渲染分类维度表格', () => {
      renderWithTheme(
        <ClassificationDimensionsTable
          dimensions={mockDimensions}
          loading={false}
          onEdit={jest.fn()}
          onDelete={jest.fn()}
        />
      )

      expect(screen.getByText('行业')).toBeInTheDocument()
      expect(screen.getByText('行业分类维度')).toBeInTheDocument()
    })

    it('应该显示加载状态', () => {
      renderWithTheme(
        <ClassificationDimensionsTable
          dimensions={[]}
          loading={true}
          onEdit={jest.fn()}
          onDelete={jest.fn()}
        />
      )

      // 检查表格是否显示加载状态
      expect(document.querySelector('.ant-spin')).toBeInTheDocument()
    })
  })

  describe('ClassificationValuesTable', () => {
    it('应该正确渲染分类值表格', () => {
      renderWithTheme(
        <ClassificationValuesTable
          values={mockValues}
          dimensions={mockDimensions}
          loading={false}
          onEdit={jest.fn()}
          onDelete={jest.fn()}
        />
      )

      expect(screen.getByText('金融')).toBeInTheDocument()
      expect(screen.getByText('finance')).toBeInTheDocument()
    })
  })

  describe('TagClassificationsTable', () => {
    it('应该正确渲染标签分类表格', () => {
      renderWithTheme(
        <TagClassificationsTable
          classifications={mockClassifications}
          loading={false}
          onEdit={jest.fn()}
          onDelete={jest.fn()}
        />
      )

      expect(screen.getByText('金融行业')).toBeInTheDocument()
      expect(screen.getByText('industry_finance')).toBeInTheDocument()
    })

    it('应该禁用系统分类的编辑和删除按钮', () => {
      const systemClassification = {
        ...mockClassifications[0],
        is_system: true
      }

      renderWithTheme(
        <TagClassificationsTable
          classifications={[systemClassification]}
          loading={false}
          onEdit={jest.fn()}
          onDelete={jest.fn()}
        />
      )

      // 检查系统分类标签是否显示
      expect(screen.getByText('系统')).toBeInTheDocument()
    })
  })
})
