/**
 * 分类值模态框组件
 */

'use client'

import React, { useEffect, useState } from 'react'
import { Modal, Form, Input, InputNumber, Select, TreeSelect, message } from 'antd'
import { 
  ClassificationValueResponse, 
  ClassificationValueCreate, 
  ClassificationDimensionResponse 
} from '@/types/api'

const { TextArea } = Input
const { Option } = Select

interface ClassificationValueModalProps {
  visible: boolean
  editingData?: ClassificationValueResponse | null
  dimensions: ClassificationDimensionResponse[]
  values: ClassificationValueResponse[]
  onCancel: () => void
  onSubmit: (data: ClassificationValueCreate) => Promise<void>
}

/**
 * 分类值模态框组件
 */
export const ClassificationValueModal: React.FC<ClassificationValueModalProps> = ({
  visible,
  editingData,
  dimensions,
  values,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [selectedDimensionId, setSelectedDimensionId] = useState<number | undefined>()

  const isEditing = !!editingData

  useEffect(() => {
    if (visible) {
      if (editingData) {
        form.setFieldsValue({
          dimension_id: editingData.dimension_id,
          value_code: editingData.value_code,
          display_name: editingData.display_name,
          description: editingData.description,
          parent_id: editingData.parent_id,
          sort_order: editingData.sort_order,
        })
        setSelectedDimensionId(editingData.dimension_id)
      } else {
        form.resetFields()
        setSelectedDimensionId(undefined)
        // 设置默认值
        form.setFieldsValue({
          sort_order: 0,
        })
      }
    }
  }, [visible, editingData, form])

  /**
   * 生成分类值代码
   */
  const generateValueCode = (displayName: string): string => {
    return displayName
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^\w\-_]/g, '')
  }

  /**
   * 构建父分类树数据
   */
  const buildParentTreeData = () => {
    if (!selectedDimensionId) return []
    
    const dimensionValues = values.filter(v => v.dimension_id === selectedDimensionId)
    
    const buildTree = (parentId: number | null = null): any[] => {
      return dimensionValues
        .filter(value => value.parent_id === parentId)
        .map(value => ({
          title: value.display_name,
          value: value.id,
          key: value.id,
          children: buildTree(value.id),
        }))
    }

    return buildTree()
  }

  /**
   * 提交表单
   */
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true)

      const valueData: ClassificationValueCreate = {
        dimension_id: values.dimension_id,
        value_code: generateValueCode(values.display_name),
        display_name: values.display_name,
        description: values.description || '',
        parent_id: values.parent_id || undefined,
        sort_order: values.sort_order || 0,
      }

      await onSubmit(valueData)
    } catch (error) {
      console.error('保存分类值失败:', error)
      message.error('保存失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 处理取消
   */
  const handleCancel = () => {
    form.resetFields()
    setSelectedDimensionId(undefined)
    onCancel()
  }

  /**
   * 处理维度选择变化
   */
  const handleDimensionChange = (dimensionId: number) => {
    setSelectedDimensionId(dimensionId)
    // 清空父分类选择
    form.setFieldsValue({ parent_id: undefined })
  }

  return (
    <Modal
      title={isEditing ? '编辑分类值' : '创建分类值'}
      open={visible}
      onCancel={handleCancel}
      onOk={() => form.submit()}
      confirmLoading={loading}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        preserve={false}
      >
        <Form.Item
          name="dimension_id"
          label="所属维度"
          rules={[{ required: true, message: '请选择所属维度' }]}
        >
          <Select 
            placeholder="请选择所属维度"
            onChange={handleDimensionChange}
            disabled={isEditing}
          >
            {dimensions.map(dimension => (
              <Option key={dimension.id} value={dimension.id}>
                {dimension.display_name}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="display_name"
          label="显示名称"
          rules={[
            { required: true, message: '请输入显示名称' },
            { min: 2, message: '显示名称至少2个字符' },
            { max: 100, message: '显示名称不能超过100个字符' },
          ]}
        >
          <Input placeholder="请输入显示名称" />
        </Form.Item>

        <Form.Item
          name="parent_id"
          label="父分类"
          help="选择父分类可以创建层级分类结构"
        >
          <TreeSelect
            placeholder="请选择父分类（可选）"
            allowClear
            treeData={buildParentTreeData()}
            treeDefaultExpandAll
            disabled={!selectedDimensionId}
          />
        </Form.Item>

        <Form.Item
          name="description"
          label="描述"
        >
          <TextArea 
            placeholder="请输入分类值描述" 
            rows={3}
            maxLength={500}
            showCount
          />
        </Form.Item>

        <Form.Item
          name="sort_order"
          label="排序"
          help="数字越小排序越靠前"
        >
          <InputNumber 
            min={0} 
            max={9999} 
            style={{ width: '100%' }} 
            placeholder="请输入排序值"
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default ClassificationValueModal
