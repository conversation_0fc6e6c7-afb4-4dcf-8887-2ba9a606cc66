/**
 * 标签表格组件
 * 支持标签的展示、编辑、删除等操作
 */

'use client'

import React, { useState } from 'react'
import { Table, Button, Space, Popconfirm, Tag, Tooltip, Badge, message } from 'antd'
import { EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { TagResponse, TagClassificationResponse } from '@/types/api'
import { formatDateTime, formatRelativeTime } from '@/utils/formatUtils'
import { tagsAPI } from '@/services/tagsAPI'

interface TagsTableProps {
  tags: TagResponse[]
  loading: boolean
  total: number
  current: number
  pageSize: number
  onEdit: (tag: TagResponse) => void
  onDelete: (tagId: number) => void
  onRefresh: () => void
  onPageChange: (page: number, pageSize: number) => void
  tagClassifications: TagClassificationResponse[]
}

/**
 * 标签表格组件
 */
export const TagsTable: React.FC<TagsTableProps> = ({
  tags,
  loading,
  total,
  current,
  pageSize,
  onEdit,
  onDelete,
  onRefresh,
  onPageChange,
  tagClassifications
}) => {
  const [deletingIds, setDeletingIds] = useState<Set<number>>(new Set())

  /**
   * 处理删除标签
   */
  const handleDelete = async (tagId: number) => {
    try {
      setDeletingIds(prev => new Set(prev).add(tagId))
      await tagsAPI.deleteTag(tagId)
      message.success('标签删除成功')
      onDelete(tagId)
      onRefresh()
    } catch (error) {
      console.error('删除标签失败:', error)
      message.error('删除失败，请重试')
    } finally {
      setDeletingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(tagId)
        return newSet
      })
    }
  }

  /**
   * 获取标签分类名称
   */
  const getTagClassificationName = (classificationId: number) => {
    const classification = tagClassifications.find(cls => cls.id === classificationId)
    return classification?.classification_name || '未知分类'
  }

  /**
   * 格式化生命周期阶段
   */
  const formatLifecycleStage = (stage: string) => {
    const stageMap = {
      draft: { text: '草稿', color: 'default' },
      active: { text: '活跃', color: 'success' },
      deprecated: { text: '已弃用', color: 'warning' },
      retired: { text: '已退役', color: 'error' },
    }
    return stageMap[stage as keyof typeof stageMap] || { text: stage, color: 'default' }
  }

  /**
   * 格式化权重信息
   */
  const formatWeightInfo = (tag: TagResponse) => {
    const baseWeight = Number(tag.base_weight) || 0
    const popularityWeight = Number(tag.popularity_weight) || 0
    const qualityWeight = Number(tag.quality_weight) || 0
    
    return (
      <div style={{ fontSize: '12px', color: '#666' }}>
        <div>基础权重: {baseWeight.toFixed(2)}</div>
        <div>流行度: {popularityWeight.toFixed(2)}</div>
        <div>质量度: {qualityWeight.toFixed(2)}</div>
      </div>
    )
  }

  /**
   * 计算总权重
   */
  const calculateTotalWeight = (tag: TagResponse): number => {
    const baseWeight = Number(tag.base_weight) || 0
    const popularityWeight = Number(tag.popularity_weight) || 0
    const qualityWeight = Number(tag.quality_weight) || 0
    const total = baseWeight + popularityWeight + qualityWeight
    return isNaN(total) ? 0 : total
  }

  /**
   * 表格列定义
   */
  const columns: ColumnsType<TagResponse> = [
    {
      title: '标签信息',
      dataIndex: 'tag_name',
      key: 'tag_name',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Tag color={record.color || 'blue'}>
              {record.tag_name}
            </Tag>
            {record.is_system && (
              <Badge count="系统" style={{ backgroundColor: '#52c41a' }} />
            )}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            代码: {record.tag_code}
          </div>
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'classification_id',
      key: 'classification_id',
      width: 120,
      render: (classificationId) => (
        <Tag color="blue">{getTagClassificationName(classificationId)}</Tag>
      ),
    },
    {
      title: '层级',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level) => `L${level}`,
    },
    {
      title: '使用统计',
      key: 'usage_stats',
      width: 120,
      render: (_, record) => (
        <div style={{ fontSize: '12px' }}>
          <div>总使用: {Number(record.usage_count) || 0}</div>
          <div>日使用: {Number(record.daily_usage_count) || 0}</div>
          <div style={{ color: '#666' }}>
            上次使用: {record.last_used_at ? formatRelativeTime(record.last_used_at) : '未使用'}
          </div>
        </div>
      ),
    },
    {
      title: '反馈',
      key: 'feedback',
      width: 100,
      render: (_, record) => (
        <div style={{ fontSize: '12px' }}>
          <div style={{ color: '#52c41a' }}>👍 {Number(record.positive_feedback_count) || 0}</div>
          <div style={{ color: '#f5222d' }}>👎 {Number(record.negative_feedback_count) || 0}</div>
        </div>
      ),
    },
    {
      title: '权重',
      key: 'weights',
      width: 100,
      render: (_, record) => (
        <Tooltip title={formatWeightInfo(record)}>
          <div style={{ fontSize: '12px', textAlign: 'center' }}>
            <div>总: {calculateTotalWeight(record).toFixed(2)}</div>
            <div style={{ color: '#666' }}>详情</div>
          </div>
        </Tooltip>
      ),
    },
    {
      title: '生命周期',
      dataIndex: 'lifecycle_stage',
      key: 'lifecycle_stage',
      width: 100,
      render: (stage) => {
        const stageInfo = formatLifecycleStage(stage)
        return <Tag color={stageInfo.color}>{stageInfo.text}</Tag>
      },
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (isActive) => (
        <Badge 
          status={isActive ? 'success' : 'error'} 
          text={isActive ? '活跃' : '停用'} 
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date) => (
        <Tooltip title={formatDateTime(date)}>
          <span style={{ fontSize: '12px' }}>
            {formatRelativeTime(date)}
          </span>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => {
                // TODO: 实现查看详情功能
                message.info('查看详情功能待实现')
              }}
            />
          </Tooltip>
          
          <Tooltip title="编辑">
            <Button 
              type="link" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => onEdit(record)}
            />
          </Tooltip>
          
          {!record.is_system && (
            <Tooltip title="删除">
              <Popconfirm
                title="确定要删除这个标签吗？"
                description="删除后无法恢复，相关数据也会被清除"
                onConfirm={() => handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
                okButtonProps={{ danger: true }}
              >
                <Button 
                  type="link" 
                  icon={<DeleteOutlined />} 
                  size="small"
                  danger
                  loading={deletingIds.has(record.id)}
                />
              </Popconfirm>
            </Tooltip>
          )}
        </Space>
      ),
    },
  ]

  return (
    <Table<TagResponse>
      columns={columns}
      dataSource={Array.isArray(tags) ? tags : []}
      loading={loading}
      rowKey="id"
      pagination={{
        current,
        pageSize,
        total,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 项，共 ${total} 项`,
        pageSizeOptions: ['10', '20', '50', '100'],
        onChange: onPageChange,
        onShowSizeChange: onPageChange,
      }}
      scroll={{ x: 1400 }}
      size="middle"
    />
  )
}

export default TagsTable 