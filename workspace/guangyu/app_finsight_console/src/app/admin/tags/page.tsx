/**
 * 标签管理页面
 */

'use client'

import React, { useState, useEffect, useCallback, Suspense } from 'react'
import { Tabs, Button, Space, message, Alert } from 'antd'
import { PlusOutlined, ReloadOutlined, BugOutlined } from '@ant-design/icons'
import { useSearchParams } from 'next/navigation'
import styled from 'styled-components'

import { ProtectedRoute } from '@/components/shared/ProtectedRoute'
import { DashboardLayout } from '@/components/Layout/DashboardLayout'
import { TagClassificationsTable } from '@/components/tags/TagClassificationsTable'
import { ClassificationDimensionsTable } from '@/components/tags/ClassificationDimensionsTable'
import { ClassificationValuesTable } from '@/components/tags/ClassificationValuesTable'
import { TagsTable } from '@/components/tags/TagsTable'
import { TagClassificationModal } from '@/components/tags/TagClassificationModal'
import { ClassificationDimensionModal } from '@/components/tags/ClassificationDimensionModal'
import { ClassificationValueModal } from '@/components/tags/ClassificationValueModal'
import { TagModal } from '@/components/tags/TagModal'
import { tagsAPI } from '@/services/tagsAPI'
import { ApiError } from '@/services/api'
import {
  TagClassificationResponse,
  ClassificationDimensionResponse,
  ClassificationValueResponse,
  TagResponse,
  TagClassificationCreate,
  ClassificationDimensionCreate,
  ClassificationValueCreate,
  TagCreate,
  GetTagClassificationsParams,
  GetClassificationDimensionsParams,
  GetClassificationValuesParams,
  GetTagsParams,
} from '@/types/api'
import { usePermissions } from '@/contexts/PermissionContext'
import { PERMISSIONS } from '@/constants'
import type { Viewport } from 'next'

const { TabPane } = Tabs

/**
 * 视口配置
 */
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
}

/**
 * 页面容器
 */
const PageContainer = styled.div`
  padding: 0;
  min-height: 100%;
  background: ${props => props.theme.colors.background.primary};
`

/**
 * 页面头部
 */
const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.lg};
  padding: ${props => props.theme.spacing.lg};
  background: ${props => props.theme.colors.background.secondary};
  border-radius: ${props => props.theme.borderRadius.lg};
  border: 1px solid ${props => props.theme.colors.border.default};
`

/**
 * 内容区域
 */
const ContentArea = styled.div`
  background: ${props => props.theme.colors.background.secondary};
  border-radius: ${props => props.theme.borderRadius.lg};
  border: 1px solid ${props => props.theme.colors.border.default};
  overflow: hidden;
  
  .ant-tabs-content-holder {
    padding: ${props => props.theme.spacing.lg};
  }
`

/**
 * 调试面板
 */
const DebugPanel = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`

/**
 * 标签管理页面内容组件
 */
const TagsPageContent: React.FC = () => {
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState('classifications')
  const [debugMode, setDebugMode] = useState(false)
  const { permissions } = usePermissions()

  // 标签分类相关状态
  const [tagClassifications, setTagClassifications] = useState<TagClassificationResponse[]>([])
  const [tagClassificationsLoading, setTagClassificationsLoading] = useState(false)
  const [tagClassificationModalVisible, setTagClassificationModalVisible] = useState(false)
  const [editingTagClassification, setEditingTagClassification] = useState<TagClassificationResponse | null>(null)

  // 分类维度相关状态
  const [classificationDimensions, setClassificationDimensions] = useState<ClassificationDimensionResponse[]>([])
  const [classificationDimensionsLoading, setClassificationDimensionsLoading] = useState(false)
  const [classificationDimensionModalVisible, setClassificationDimensionModalVisible] = useState(false)
  const [editingClassificationDimension, setEditingClassificationDimension] = useState<ClassificationDimensionResponse | null>(null)

  // 分类值相关状态
  const [classificationValues, setClassificationValues] = useState<ClassificationValueResponse[]>([])
  const [classificationValuesTotal, setClassificationValuesTotal] = useState(0)
  const [classificationValuesLoading, setClassificationValuesLoading] = useState(false)
  const [classificationValueModalVisible, setClassificationValueModalVisible] = useState(false)
  const [editingClassificationValue, setEditingClassificationValue] = useState<ClassificationValueResponse | null>(null)
  const [currentClassificationValuesParams, setCurrentClassificationValuesParams] = useState<GetClassificationValuesParams>({
    page: 1,
    size: 20
  })

  // 标签相关状态
  const [tags, setTags] = useState<TagResponse[]>([])
  const [tagsTotal, setTagsTotal] = useState(0)
  const [tagsLoading, setTagsLoading] = useState(false)
  const [tagModalVisible, setTagModalVisible] = useState(false)
  const [editingTag, setEditingTag] = useState<TagResponse | null>(null)
  const [currentTagsParams, setCurrentTagsParams] = useState<GetTagsParams>({
    page: 1,
    size: 10
  })

  // ==================== 标签分类管理 ====================

  /**
   * 获取标签分类列表
   */
  const fetchTagClassifications = useCallback(async (params: GetTagClassificationsParams = {}) => {
    try {
      setTagClassificationsLoading(true)
      const response = await tagsAPI.getTagClassifications(params)

      if (debugMode) {
        console.log('标签分类列表API响应:', response)
      }

      // 处理分页响应
      if (response && typeof response === 'object' && 'items' in response) {
        setTagClassifications(response.items || [])
      } else {
        console.warn('标签分类API返回的数据格式不正确:', response)
        setTagClassifications([])
      }
    } catch (error) {
      console.error('获取标签分类列表失败:', error)
      if (error instanceof ApiError) {
        message.error(error.message)
      } else {
        message.error('获取标签分类列表失败，请重试')
      }
      setTagClassifications([])
    } finally {
      setTagClassificationsLoading(false)
    }
  }, [debugMode])

  /**
   * 处理创建标签分类
   */
  const handleCreateTagClassification = () => {
    setEditingTagClassification(null)
    setTagClassificationModalVisible(true)
  }

  /**
   * 处理编辑标签分类
   */
  const handleEditTagClassification = (classification: TagClassificationResponse) => {
    setEditingTagClassification(classification)
    setTagClassificationModalVisible(true)
  }

  /**
   * 处理删除标签分类
   */
  const handleDeleteTagClassification = async (classificationId: number) => {
    try {
      await tagsAPI.deleteTagClassification(classificationId)
      message.success('删除成功')
      fetchTagClassifications()
    } catch (error) {
      console.error('删除标签分类失败:', error)
      if (error instanceof ApiError) {
        message.error(error.message)
      } else {
        message.error('删除失败，请重试')
      }
    }
  }

  /**
   * 处理标签分类提交
   */
  const handleTagClassificationSubmit = async (values: TagClassificationCreate) => {
    try {
      if (editingTagClassification) {
        await tagsAPI.updateTagClassification(editingTagClassification.id, values)
        message.success('更新成功')
      } else {
        await tagsAPI.createTagClassification(values)
        message.success('创建成功')
      }
      setTagClassificationModalVisible(false)
      setEditingTagClassification(null)
      fetchTagClassifications()
    } catch (error) {
      console.error('保存标签分类失败:', error)
      if (error instanceof ApiError) {
        message.error(error.message)
      } else {
        message.error('保存失败，请重试')
      }
    }
  }

  // ==================== 分类维度管理 ====================

  /**
   * 获取分类维度列表
   */
  const fetchClassificationDimensions = useCallback(async (params: GetClassificationDimensionsParams = {}) => {
    try {
      setClassificationDimensionsLoading(true)
      const response = await tagsAPI.getClassificationDimensions(params)

      if (debugMode) {
        console.log('分类维度列表API响应:', response)
      }

      setClassificationDimensions(Array.isArray(response) ? response : [])
    } catch (error) {
      console.error('获取分类维度列表失败:', error)
      if (error instanceof ApiError) {
        message.error(error.message)
      } else {
        message.error('获取分类维度列表失败，请重试')
      }
      setClassificationDimensions([])
    } finally {
      setClassificationDimensionsLoading(false)
    }
  }, [debugMode])

  /**
   * 处理创建分类维度
   */
  const handleCreateClassificationDimension = () => {
    setEditingClassificationDimension(null)
    setClassificationDimensionModalVisible(true)
  }

  /**
   * 处理编辑分类维度
   */
  const handleEditClassificationDimension = (dimension: ClassificationDimensionResponse) => {
    setEditingClassificationDimension(dimension)
    setClassificationDimensionModalVisible(true)
  }

  /**
   * 处理删除分类维度
   */
  const handleDeleteClassificationDimension = async (dimensionId: number) => {
    try {
      await tagsAPI.deleteClassificationDimension(dimensionId)
      message.success('删除成功')
      fetchClassificationDimensions()
    } catch (error) {
      console.error('删除分类维度失败:', error)
      if (error instanceof ApiError) {
        message.error(error.message)
      } else {
        message.error('删除失败，请重试')
      }
    }
  }

  /**
   * 处理分类维度提交
   */
  const handleClassificationDimensionSubmit = async (values: ClassificationDimensionCreate) => {
    try {
      if (editingClassificationDimension) {
        await tagsAPI.updateClassificationDimension(editingClassificationDimension.id, values)
        message.success('更新成功')
      } else {
        await tagsAPI.createClassificationDimension(values)
        message.success('创建成功')
      }
      setClassificationDimensionModalVisible(false)
      setEditingClassificationDimension(null)
      fetchClassificationDimensions()
    } catch (error) {
      console.error('保存分类维度失败:', error)
      if (error instanceof ApiError) {
        message.error(error.message)
      } else {
        message.error('保存失败，请重试')
      }
    }
  }

  // ==================== 分类值管理 ====================

  /**
   * 获取分类值列表
   */
  const fetchClassificationValues = useCallback(async (params: GetClassificationValuesParams = {}) => {
    try {
      setClassificationValuesLoading(true)
      const response = await tagsAPI.getClassificationValues(params)

      if (debugMode) {
        console.log('分类值列表API响应:', response)
      }

      // 处理分页响应
      if (response && typeof response === 'object' && 'items' in response) {
        setClassificationValues(response.items || [])
        setClassificationValuesTotal(response.total || 0)
        setCurrentClassificationValuesParams(params)
      } else {
        console.warn('分类值API返回的数据格式不正确:', response)
        setClassificationValues([])
        setClassificationValuesTotal(0)
      }
    } catch (error) {
      console.error('获取分类值列表失败:', error)
      if (error instanceof ApiError) {
        message.error(error.message)
      } else {
        message.error('获取分类值列表失败，请重试')
      }
      setClassificationValues([])
      setClassificationValuesTotal(0)
    } finally {
      setClassificationValuesLoading(false)
    }
  }, [debugMode])

  /**
   * 处理创建分类值
   */
  const handleCreateClassificationValue = () => {
    setEditingClassificationValue(null)
    setClassificationValueModalVisible(true)
  }

  /**
   * 处理编辑分类值
   */
  const handleEditClassificationValue = (value: ClassificationValueResponse) => {
    setEditingClassificationValue(value)
    setClassificationValueModalVisible(true)
  }

  /**
   * 处理删除分类值
   */
  const handleDeleteClassificationValue = async (valueId: number) => {
    try {
      await tagsAPI.deleteClassificationValue(valueId)
      message.success('删除成功')
      fetchClassificationValues(currentClassificationValuesParams)
    } catch (error) {
      console.error('删除分类值失败:', error)
      if (error instanceof ApiError) {
        message.error(error.message)
      } else {
        message.error('删除失败，请重试')
      }
    }
  }

  /**
   * 处理分类值提交
   */
  const handleClassificationValueSubmit = async (values: ClassificationValueCreate) => {
    try {
      if (editingClassificationValue) {
        await tagsAPI.updateClassificationValue(editingClassificationValue.id, values)
        message.success('更新成功')
      } else {
        await tagsAPI.createClassificationValue(values)
        message.success('创建成功')
      }
      setClassificationValueModalVisible(false)
      setEditingClassificationValue(null)
      fetchClassificationValues(currentClassificationValuesParams)
    } catch (error) {
      console.error('保存分类值失败:', error)
      if (error instanceof ApiError) {
        message.error(error.message)
      } else {
        message.error('保存失败，请重试')
      }
    }
  }

  // ==================== 标签管理 ====================

  /**
   * 获取标签列表
   */
  const fetchTags = useCallback(async (params: GetTagsParams = {}) => {
    try {
      setTagsLoading(true)
      const response = await tagsAPI.getTags(params)

      if (debugMode) {
        console.log('标签列表API响应:', response)
      }

      // 处理分页响应
      if (response && typeof response === 'object' && 'items' in response) {
        setTags(response.items || [])
        setTagsTotal(response.total || 0)
        setCurrentTagsParams(params)
      } else {
        console.warn('标签API返回的数据格式不正确:', response)
        setTags([])
        setTagsTotal(0)
      }
    } catch (error) {
      console.error('获取标签列表失败:', error)
      if (error instanceof ApiError) {
        message.error(error.message)
      } else {
        message.error('获取标签列表失败，请重试')
      }
      setTags([])
      setTagsTotal(0)
    } finally {
      setTagsLoading(false)
    }
  }, [debugMode])

  /**
   * 处理创建标签
   */
  const handleCreateTag = () => {
    setEditingTag(null)
    setTagModalVisible(true)
  }

  /**
   * 处理编辑标签
   */
  const handleEditTag = (tag: TagResponse) => {
    setEditingTag(tag)
    setTagModalVisible(true)
  }

  /**
   * 处理删除标签
   */
  const handleDeleteTag = async (tagId: number) => {
    try {
      await tagsAPI.deleteTag(tagId)
      message.success('删除成功')
      fetchTags(currentTagsParams)
    } catch (error) {
      console.error('删除标签失败:', error)
      if (error instanceof ApiError) {
        message.error(error.message)
      } else {
        message.error('删除失败，请重试')
      }
    }
  }

  /**
   * 处理标签提交
   */
  const handleTagSubmit = async (values: TagCreate) => {
    try {
      if (editingTag) {
        await tagsAPI.updateTag(editingTag.id, values)
        message.success('更新成功')
      } else {
        await tagsAPI.createTag(values)
        message.success('创建成功')
      }
      setTagModalVisible(false)
      setEditingTag(null)
      fetchTags(currentTagsParams)
    } catch (error) {
      console.error('保存标签失败:', error)
      if (error instanceof ApiError) {
        message.error(error.message)
      } else {
        message.error('保存失败，请重试')
      }
    }
  }

  // ==================== 通用方法 ====================

  /**
   * 处理标签分页切换
   */
  const handleTagsPageChange = (page: number, pageSize: number) => {
    const newParams = {
      ...currentTagsParams,
      page,
      size: pageSize
    }
    fetchTags(newParams)
  }

  /**
   * 处理分类值分页切换
   */
  const handleClassificationValuesPageChange = (page: number, pageSize: number) => {
    const newParams = {
      ...currentClassificationValuesParams,
      page,
      size: pageSize
    }
    fetchClassificationValues(newParams)
  }

  /**
   * 处理刷新
   */
  const handleRefresh = () => {
    switch (activeTab) {
      case 'classifications':
        fetchTagClassifications()
        break
      case 'dimensions':
        fetchClassificationDimensions()
        break
      case 'values':
        fetchClassificationValues(currentClassificationValuesParams)
        break
      case 'tags':
        fetchTags(currentTagsParams)
        break
    }
  }

  /**
   * 切换调试模式
   */
  const toggleDebugMode = () => {
    setDebugMode(!debugMode)
  }

  /**
   * 处理Tab切换
   */
  const handleTabChange = (key: string) => {
    setActiveTab(key)
  }

  // 页面初始化
  useEffect(() => {
    console.log('标签页面初始化 - 开始加载数据')
    fetchTagClassifications()
    fetchClassificationDimensions()
    fetchClassificationValues(currentClassificationValuesParams)
    fetchTags(currentTagsParams)

    const shouldRefresh = searchParams.get('refresh') === 'true'
    if (shouldRefresh) {
      const newUrl = window.location.pathname
      window.history.replaceState({}, '', newUrl)
    }
  }, [fetchTagClassifications, fetchClassificationDimensions, fetchClassificationValues, fetchTags, currentClassificationValuesParams, currentTagsParams, searchParams])

  // 添加调试信息
  useEffect(() => {
    console.log('标签页面数据状态:', {
      tagClassifications: { isArray: Array.isArray(tagClassifications), length: tagClassifications?.length, data: tagClassifications },
      classificationDimensions: { isArray: Array.isArray(classificationDimensions), length: classificationDimensions?.length, data: classificationDimensions },
      classificationValues: { isArray: Array.isArray(classificationValues), length: classificationValues?.length, data: classificationValues },
      tags: { isArray: Array.isArray(tags), length: tags?.length, data: tags },
    })
  }, [tagClassifications, classificationDimensions, classificationValues, tags])

  return (
    <PageContainer>
      {/* 调试面板 */}
      {debugMode && (
        <DebugPanel>
          <Alert
            message="调试模式已开启"
            description={`当前权限: ${permissions.join(', ')}`}
            type="info"
            showIcon
            closable
            onClose={() => setDebugMode(false)}
          />
        </DebugPanel>
      )}

      {/* 页面头部 */}
      <PageHeader>
        <h1>标签管理</h1>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              switch (activeTab) {
                case 'classifications':
                  handleCreateTagClassification()
                  break
                case 'dimensions':
                  handleCreateClassificationDimension()
                  break
                case 'values':
                  handleCreateClassificationValue()
                  break
                case 'tags':
                  handleCreateTag()
                  break
              }
            }}
          >
            新建
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
          >
            刷新
          </Button>
          <Button
            icon={<BugOutlined />}
            onClick={toggleDebugMode}
          >
            调试
          </Button>
        </Space>
      </PageHeader>

      {/* 内容区域 */}
      <ContentArea>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab={`标签分类 (${tagClassifications.length})`} key="classifications">
            <TagClassificationsTable
              classifications={tagClassifications}
              loading={tagClassificationsLoading}
              onEdit={handleEditTagClassification}
              onDelete={handleDeleteTagClassification}
            />
          </TabPane>
          <TabPane tab={`分类维度 (${classificationDimensions.length})`} key="dimensions">
            <ClassificationDimensionsTable
              dimensions={classificationDimensions}
              loading={classificationDimensionsLoading}
              onEdit={handleEditClassificationDimension}
              onDelete={handleDeleteClassificationDimension}
            />
          </TabPane>
          <TabPane tab={`分类值 (${classificationValuesTotal})`} key="values">
            <ClassificationValuesTable
              values={classificationValues}
              dimensions={classificationDimensions}
              loading={classificationValuesLoading}
              onEdit={handleEditClassificationValue}
              onDelete={handleDeleteClassificationValue}
            />
          </TabPane>
          <TabPane tab={`标签 (${tagsTotal})`} key="tags">
            <TagsTable
              tags={tags}
              loading={tagsLoading}
              total={tagsTotal}
              current={currentTagsParams.page || 1}
              pageSize={currentTagsParams.size || 10}
              onEdit={handleEditTag}
              onDelete={handleDeleteTag}
              onRefresh={() => fetchTags(currentTagsParams)}
              onPageChange={handleTagsPageChange}
              tagClassifications={tagClassifications}
            />
          </TabPane>
        </Tabs>
      </ContentArea>

      {/* 模态框 */}
      <TagClassificationModal
        visible={tagClassificationModalVisible}
        editingData={editingTagClassification || undefined}
        classifications={tagClassifications}
        onCancel={() => {
          setTagClassificationModalVisible(false)
          setEditingTagClassification(null)
        }}
        onSubmit={handleTagClassificationSubmit}
      />

      <ClassificationDimensionModal
        visible={classificationDimensionModalVisible}
        editingData={editingClassificationDimension || undefined}
        onCancel={() => {
          setClassificationDimensionModalVisible(false)
          setEditingClassificationDimension(null)
        }}
        onSubmit={handleClassificationDimensionSubmit}
      />

      <ClassificationValueModal
        visible={classificationValueModalVisible}
        editingData={editingClassificationValue || undefined}
        dimensions={classificationDimensions}
        values={classificationValues}
        onCancel={() => {
          setClassificationValueModalVisible(false)
          setEditingClassificationValue(null)
        }}
        onSubmit={handleClassificationValueSubmit}
      />

      <TagModal
        visible={tagModalVisible}
        editingData={editingTag || undefined}
        tagTypes={[]}
        tagCategories={[]}
        tags={tags}
        onCancel={() => {
          setTagModalVisible(false)
          setEditingTag(null)
        }}
        onSubmit={handleTagSubmit}
      />
    </PageContainer>
  )
}

/**
 * 标签管理页面
 */
const TagsPage: React.FC = () => {
  return (
    <ProtectedRoute permissions={[PERMISSIONS.TAG.READ]}>
      <DashboardLayout>
        <Suspense fallback={<div>Loading...</div>}>
          <TagsPageContent />
        </Suspense>
      </DashboardLayout>
    </ProtectedRoute>
  )
}

export default TagsPage 