/**
 * 标签管理API服务
 * 包含标签分类、标签的CRUD操作
 */

import { apiService } from './api'
import {
  TagClassificationCreate,
  TagClassificationUpdate,
  TagClassificationResponse,
  TagClassificationListResponse,
  GetTagClassificationsParams,
  ClassificationDimensionCreate,
  ClassificationDimensionUpdate,
  ClassificationDimensionResponse,
  ClassificationDimensionListResponse,
  GetClassificationDimensionsParams,
  ClassificationValueCreate,
  ClassificationValueUpdate,
  ClassificationValueResponse,
  ClassificationValueListResponse,
  GetClassificationValuesParams,
  TagCreate,
  TagUpdate,
  TagResponse,
  GetTagsParams,
  TagListResponse,
  OperationResult,
  LifecycleStage,
} from '@/types/api'

/**
 * 标签API服务类
 */
export class TagsAPI {
  // ==================== 标签分类管理 ====================

  /**
   * 获取标签分类列表
   * @param params 查询参数
   */
  async getTagClassifications(params: GetTagClassificationsParams = {}): Promise<TagClassificationListResponse> {
    try {
      const response = await apiService.get<TagClassificationListResponse | TagClassificationResponse[]>('api/v1/admin/tags/classifications', params)

      // 确保返回正确的数据结构
      if (response && typeof response === 'object' && !Array.isArray(response) && 'items' in response) {
        return {
          items: Array.isArray(response.items) ? response.items : [],
          total: response.total || 0,
          page: response.page || 1,
          size: response.size || 20,
          pages: response.pages || 1,
        }
      }

      // 如果响应直接是数组（兼容旧版本API），包装成分页格式
      if (Array.isArray(response)) {
        return {
          items: response,
          total: response.length,
          page: 1,
          size: response.length,
          pages: 1,
        }
      }

      // 默认返回空数据
      return {
        items: [],
        total: 0,
        page: 1,
        size: 20,
        pages: 1,
      }
    } catch (error) {
      console.error('获取标签分类列表失败:', error)
      return {
        items: [],
        total: 0,
        page: 1,
        size: 20,
        pages: 1,
      }
    }
  }

  /**
   * 获取标签分类详情
   * @param classificationId 标签分类ID
   */
  async getTagClassificationDetail(classificationId: number): Promise<TagClassificationResponse> {
    return apiService.get<TagClassificationResponse>(`api/v1/admin/tags/classifications/${classificationId}`)
  }

  /**
   * 创建标签分类
   * @param classificationData 标签分类数据
   */
  async createTagClassification(classificationData: TagClassificationCreate): Promise<TagClassificationResponse> {
    return apiService.post<TagClassificationResponse>('api/v1/admin/tags/classifications', classificationData)
  }

  /**
   * 更新标签分类
   * @param classificationId 标签分类ID
   * @param classificationData 更新数据
   */
  async updateTagClassification(classificationId: number, classificationData: TagClassificationUpdate): Promise<TagClassificationResponse> {
    return apiService.put<TagClassificationResponse>(`api/v1/admin/tags/classifications/${classificationId}`, classificationData)
  }

  /**
   * 删除标签分类
   * @param classificationId 标签分类ID
   */
  async deleteTagClassification(classificationId: number): Promise<OperationResult> {
    await apiService.delete(`api/v1/admin/tags/classifications/${classificationId}`)
    return { success: true, message: '标签分类删除成功' }
  }

  // ==================== 分类维度管理 ====================

  /**
   * 获取分类维度列表
   * @param params 查询参数
   */
  async getClassificationDimensions(params: GetClassificationDimensionsParams = {}): Promise<ClassificationDimensionResponse[]> {
    try {
      const response = await apiService.get<ClassificationDimensionResponse[]>('api/v1/admin/classifications/dimensions', params)
      return Array.isArray(response) ? response : []
    } catch (error) {
      console.error('获取分类维度列表失败:', error)
      return []
    }
  }

  /**
   * 获取分类维度详情
   * @param dimensionId 维度ID
   */
  async getClassificationDimensionDetail(dimensionId: number): Promise<ClassificationDimensionResponse> {
    return apiService.get<ClassificationDimensionResponse>(`api/v1/admin/classifications/dimensions/${dimensionId}`)
  }

  /**
   * 创建分类维度
   * @param dimensionData 维度数据
   */
  async createClassificationDimension(dimensionData: ClassificationDimensionCreate): Promise<ClassificationDimensionResponse> {
    return apiService.post<ClassificationDimensionResponse>('api/v1/admin/classifications/dimensions', dimensionData)
  }

  /**
   * 更新分类维度
   * @param dimensionId 维度ID
   * @param dimensionData 更新数据
   */
  async updateClassificationDimension(dimensionId: number, dimensionData: ClassificationDimensionUpdate): Promise<ClassificationDimensionResponse> {
    return apiService.put<ClassificationDimensionResponse>(`api/v1/admin/classifications/dimensions/${dimensionId}`, dimensionData)
  }

  /**
   * 删除分类维度
   * @param dimensionId 维度ID
   */
  async deleteClassificationDimension(dimensionId: number): Promise<OperationResult> {
    await apiService.delete(`api/v1/admin/classifications/dimensions/${dimensionId}`)
    return { success: true, message: '分类维度删除成功' }
  }

  // ==================== 分类值管理 ====================

  /**
   * 获取分类值列表
   * @param params 查询参数
   */
  async getClassificationValues(params: GetClassificationValuesParams = {}): Promise<ClassificationValueListResponse> {
    try {
      const response = await apiService.get<ClassificationValueListResponse>('api/v1/admin/classifications/values', params)

      // 确保返回正确的数据结构
      if (response && typeof response === 'object' && 'items' in response) {
        return {
          items: Array.isArray(response.items) ? response.items : [],
          total: response.total || 0,
          page: response.page || 1,
          size: response.size || 20,
          pages: response.pages || 1,
        }
      }

      // 默认返回空数据
      return {
        items: [],
        total: 0,
        page: 1,
        size: 20,
        pages: 1,
      }
    } catch (error) {
      console.error('获取分类值列表失败:', error)
      return {
        items: [],
        total: 0,
        page: 1,
        size: 20,
        pages: 1,
      }
    }
  }

  /**
   * 获取分类值详情
   * @param valueId 分类值ID
   */
  async getClassificationValueDetail(valueId: number): Promise<ClassificationValueResponse> {
    return apiService.get<ClassificationValueResponse>(`api/v1/admin/classifications/values/${valueId}`)
  }

  /**
   * 创建分类值
   * @param valueData 分类值数据
   */
  async createClassificationValue(valueData: ClassificationValueCreate): Promise<ClassificationValueResponse> {
    return apiService.post<ClassificationValueResponse>('api/v1/admin/classifications/values', valueData)
  }

  /**
   * 更新分类值
   * @param valueId 分类值ID
   * @param valueData 更新数据
   */
  async updateClassificationValue(valueId: number, valueData: ClassificationValueUpdate): Promise<ClassificationValueResponse> {
    return apiService.put<ClassificationValueResponse>(`api/v1/admin/classifications/values/${valueId}`, valueData)
  }

  /**
   * 删除分类值
   * @param valueId 分类值ID
   */
  async deleteClassificationValue(valueId: number): Promise<OperationResult> {
    await apiService.delete(`api/v1/admin/classifications/values/${valueId}`)
    return { success: true, message: '分类值删除成功' }
  }

  // ==================== 标签管理 ====================

  /**
   * 获取标签列表
   * @param params 查询参数
   */
  async getTags(params: GetTagsParams = {}): Promise<TagListResponse> {
    try {
      const response = await apiService.get<TagListResponse | TagResponse[]>('api/v1/admin/tags', params)
      
      // 确保返回正确的数据结构
      if (response && typeof response === 'object' && !Array.isArray(response)) {
        return {
          items: Array.isArray(response.items) ? response.items : [],
          total: response.total || 0,
          page: response.page || 1,
          size: response.size || 10,
          pages: response.pages || 1,
        }
      }
      
      // 如果响应直接是数组，包装成TagListResponse格式
      if (Array.isArray(response)) {
        return {
          items: response,
          total: response.length,
          page: 1,
          size: response.length,
          pages: 1,
        }
      }
      
      // 默认返回空数据
      return {
        items: [],
        total: 0,
        page: 1,
        size: 10,
        pages: 1,
      }
    } catch (error) {
      console.error('获取标签列表失败:', error)
      return {
        items: [],
        total: 0,
        page: 1,
        size: 10,
        pages: 1,
      }
    }
  }

  /**
   * 获取标签详情
   * @param tagId 标签ID
   */
  async getTagDetail(tagId: number): Promise<TagResponse> {
    return apiService.get<TagResponse>(`api/v1/admin/tags/${tagId}`)
  }

  /**
   * 创建标签
   * @param tagData 标签数据
   */
  async createTag(tagData: TagCreate): Promise<TagResponse> {
    return apiService.post<TagResponse>('api/v1/admin/tags', tagData)
  }

  /**
   * 更新标签
   * @param tagId 标签ID
   * @param tagData 更新数据
   */
  async updateTag(tagId: number, tagData: TagUpdate): Promise<TagResponse> {
    return apiService.put<TagResponse>(`api/v1/admin/tags/${tagId}`, tagData)
  }

  /**
   * 删除标签
   * @param tagId 标签ID
   */
  async deleteTag(tagId: number): Promise<OperationResult> {
    await apiService.delete(`api/v1/admin/tags/${tagId}`)
    return { success: true, message: '标签删除成功' }
  }

  // ==================== 工具方法 ====================

  /**
   * 格式化生命周期阶段
   * @param stage 生命周期阶段
   */
  formatLifecycleStage(stage: LifecycleStage): { text: string; color: string } {
    switch (stage) {
      case LifecycleStage.DRAFT:
        return { text: '草稿', color: 'orange' }
      case LifecycleStage.ACTIVE:
        return { text: '激活', color: 'green' }
      case LifecycleStage.DEPRECATED:
        return { text: '已弃用', color: 'red' }
      case LifecycleStage.RETIRED:
        return { text: '已退役', color: 'gray' }
      default:
        return { text: '未知', color: 'gray' }
    }
  }

  /**
   * 格式化标签状态
   * @param isActive 是否激活
   * @param lifecycleStage 生命周期阶段
   */
  formatTagStatus(isActive: boolean, lifecycleStage: LifecycleStage): { text: string; color: string } {
    if (!isActive) {
      return { text: '已禁用', color: 'red' }
    }
    return this.formatLifecycleStage(lifecycleStage)
  }

  /**
   * 生成标签代码
   * @param tagName 标签名称
   */
  generateTagCode(tagName: string): string {
    return tagName
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^\w\-_]/g, '')
  }

  /**
   * 生成标签slug
   * @param tagName 标签名称
   */
  generateTagSlug(tagName: string): string {
    return tagName
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]/g, '')
  }

  /**
   * 构建标签层级路径
   * @param tags 标签列表
   * @param currentTag 当前标签
   */
  buildTagPath(tags: TagResponse[], currentTag: TagResponse): string {
    const path = [currentTag.tag_name]
    let parentId = currentTag.parent_id
    
    while (parentId) {
      const parent = tags.find(tag => tag.id === parentId)
      if (parent) {
        path.unshift(parent.tag_name)
        parentId = parent.parent_id
      } else {
        break
      }
    }
    
    return path.join(' > ')
  }
}

/**
 * 标签API服务实例
 */
export const tagsAPI = new TagsAPI() 