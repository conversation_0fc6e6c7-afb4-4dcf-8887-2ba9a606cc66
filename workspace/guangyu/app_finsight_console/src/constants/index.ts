/**
 * 应用常量配置
 */

// ==================== 路由常量 ====================
export const ROUTES = {
  // 认证相关
  LOGIN: '/admin/login',
  
  // 仪表板
  DASHBOARD: '/dashboard',
  
  // 用户管理 - 默认登录后跳转页面
  USERS: '/admin/users',
  USER_DETAIL: (id: string) => `/admin/users/${id}`,
  USER_EDIT: (id: string) => `/admin/users/${id}/edit`,
  USER_CREATE: '/admin/users/create',
  
  // 角色管理
  ROLES: '/admin/roles',
  ROLE_DETAIL: (id: string) => `/admin/roles/${id}`,
  ROLE_EDIT: (id: string) => `/admin/roles/${id}/edit`,
  ROLE_CREATE: '/admin/roles/create',
  
  // 权限管理
  PERMISSIONS: '/admin/permissions',
  PERMISSION_DETAIL: (id: string) => `/admin/permissions/${id}`,
  PERMISSION_EDIT: (id: string) => `/admin/permissions/${id}/edit`,
  PERMISSION_CREATE: '/admin/permissions/create',
  
  // 默认登录后跳转的页面
  DEFAULT_REDIRECT: '/admin/users',

  // 新增权限相关路由
  HOME: '/',
  ADMIN_LOGIN: '/admin/login',
  ADMIN_DASHBOARD: '/dashboard',
  ADMIN_USERS: '/admin/users',
  ADMIN_USERS_CREATE: '/admin/users/create',
  ADMIN_USERS_EDIT: (id: number | string) => `/admin/users/${id}/edit`,
  ADMIN_USERS_DETAIL: (id: number | string) => `/admin/users/${id}`,
  ADMIN_USERS_ROLES: (id: number | string) => `/admin/users/${id}/roles`,
  ADMIN_ROLES: '/admin/roles',
  ADMIN_ROLES_CREATE: '/admin/roles/create',
  ADMIN_ROLES_EDIT: (id: number | string) => `/admin/roles/${id}/edit`,
  ADMIN_ROLES_DETAIL: (id: number | string) => `/admin/roles/${id}`,
  ADMIN_PERMISSIONS: '/admin/permissions',
  ADMIN_PERMISSIONS_CREATE: '/admin/permissions/create',
  ADMIN_PERMISSIONS_EDIT: (id: number | string) => `/admin/permissions/${id}/edit`,
  ADMIN_PERMISSIONS_DETAIL: (id: number | string) => `/admin/permissions/${id}`,
  ADMIN_DEBUG_PERMISSIONS: '/admin/debug/permissions',
  
  // 数据处理规则管理
  ADMIN_DATA_PROCESSING_RULES: '/admin/data-processing-rules',
  ADMIN_DATA_PROCESSING_PIPELINES: '/admin/data-processing-pipelines',

  // 快讯管理
  ADMIN_FLASH_NEWS: '/admin/data-processing/flash-news',
} as const

// ==================== 认证常量 ====================
export const AUTH = {
  // 短信验证码倒计时时间（秒）
  SMS_RESEND_COUNTDOWN: 60,
  
  // 验证码长度
  SMS_CODE_LENGTH: 6,
  
  // Token存储key - 与userAPI中使用的保持一致
  TOKEN_STORAGE_KEY: 'access_token',
  USER_STORAGE_KEY: 'user',
  
  // 手机号正则
  PHONE_REGEX: /^1[3-9]\d{9}$/,
  
  // 新增令牌类型和令牌过期时间
  TOKEN_TYPE: 'Bearer',
  TOKEN_EXPIRY: 24 * 60 * 60 * 1000, // 24小时
} as const

// ==================== 应用信息 ====================
export const APP = {
  NAME: process.env.NEXT_PUBLIC_APP_NAME || 'FinSight控制台',
  DESCRIPTION: process.env.NEXT_PUBLIC_APP_DESCRIPTION || '金融洞察管理平台',
  VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
} as const

// ==================== API常量 ====================
export const API = {
  BASE_URL: 'http://helloapi.lightrain.vip',
  TIMEOUT: 10000,
  RETRY_TIMES: 3,
} as const

// ==================== UI常量 ====================
export const UI = {
  // 分页配置
  PAGE_SIZE_OPTIONS: ['10', '20', '50', '100'],
  DEFAULT_PAGE_SIZE: 20,
  
  // 表格配置
  TABLE_SCROLL_Y: 400,
  
  // 响应式断点
  BREAKPOINTS: {
    xs: 480,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    xxl: 1600,
  },
} as const

// ==================== 权限常量 ====================
export const PERMISSIONS = {
  // 用户管理权限
  USER_LIST_READ: 'user.list.read',
  USER_PROFILE_READ: 'user.profile.read',
  USER_CREATE: 'user.user.create',
  USER_UPDATE: 'user.user.update',
  USER_DELETE: 'user.user.delete',
  USER_STATUS_UPDATE: 'user.status.update',
  USER_ROLE_ASSIGN: 'user.role.assign',
  USER_PERMISSION_READ: 'user.permission.read',
  USER_ROLE_BATCH_ASSIGN: 'user.role.batch.assign',

  // 权限管理模块
  PERMISSION: {
    CREATE: 'permission.permission.create',
    READ: 'permission.permission.read',
    UPDATE: 'permission.permission.update',
    DELETE: 'permission.permission.delete',
    LIST_READ: 'permission.list.read',
    ANALYTICS_READ: 'permission.analytics.read'
  },

  // 角色管理模块  
  ROLE: {
    CREATE: 'role.role.create',
    READ: 'role.role.read',
    UPDATE: 'role.role.update',
    DELETE: 'role.role.delete',
    LIST_READ: 'role.list.read',
    PERMISSION_ASSIGN: 'role.permission.assign',
    PERMISSION_REVOKE: 'role.permission.revoke',
    PERMISSION_READ: 'role.permission.read',
    ANALYTICS_READ: 'role.analytics.read'
  },

  // 标签管理模块
  TAG: {
    CREATE: 'tag.tag.create',
    READ: 'tag.tag.read',
    UPDATE: 'tag.tag.update',
    DELETE: 'tag.tag.delete',
    LIST_READ: 'tag.list.read',
    CLASSIFICATION_CREATE: 'tag.classification.create',
    CLASSIFICATION_READ: 'tag.classification.read',
    CLASSIFICATION_UPDATE: 'tag.classification.update',
    CLASSIFICATION_DELETE: 'tag.classification.delete',
    ANALYTICS_READ: 'tag.analytics.read'
  },

  // 分类管理模块
  CLASSIFICATION: {
    DIMENSION_CREATE: 'classification.dimension.create',
    DIMENSION_READ: 'classification.dimension.read',
    DIMENSION_UPDATE: 'classification.dimension.update',
    DIMENSION_DELETE: 'classification.dimension.delete',
    VALUE_CREATE: 'classification.value.create',
    VALUE_READ: 'classification.value.read',
    VALUE_UPDATE: 'classification.value.update',
    VALUE_DELETE: 'classification.value.delete',
    ANALYTICS_READ: 'classification.analytics.read'
  },

  // 分类管理模块
  CLASSIFICATION: {
    CREATE: 'classification.classification.create',
    READ: 'classification.classification.read',
    UPDATE: 'classification.classification.update',
    DELETE: 'classification.classification.delete',
    LIST_READ: 'classification.list.read',
    DIMENSION_CREATE: 'classification.dimension.create',
    DIMENSION_READ: 'classification.dimension.read',
    DIMENSION_UPDATE: 'classification.dimension.update',
    DIMENSION_DELETE: 'classification.dimension.delete',
    VALUE_CREATE: 'classification.value.create',
    VALUE_READ: 'classification.value.read',
    VALUE_UPDATE: 'classification.value.update',
    VALUE_DELETE: 'classification.value.delete',
    ANALYTICS_READ: 'classification.analytics.read'
  },


  // 数据源管理
  DATA_SOURCE: {
    CREATE: 'data_source.data_source.create',
    READ: 'data_source.data_source.read',
    UPDATE: 'data_source.data_source.update',
    DELETE: 'data_source.data_source.delete',
    LIST_READ: 'data_source.list.read',
    STATS_READ: 'data_source.stats.read',
    CONFIG_CREATE: 'data_source.config.create',
    CONFIG_READ: 'data_source.config.read',
    CONFIG_LIST_READ: 'data_source.config.read',
    CONFIG_UPDATE: 'data_source.config.update',
    CONFIG_DELETE: 'data_source.config.delete',
    CONFIG_MANAGE: 'data_source.config.manage'
  },

  // 原始数据记录模块
  RAW_DATA_RECORD: {
    CREATE: 'raw_data_record.raw_data_record.create',
    READ: 'raw_data_record.raw_data_record.read',
    UPDATE: 'raw_data_record.raw_data_record.update',
    DELETE: 'raw_data_record.raw_data_record.delete',
    MANAGE: 'raw_data_record.raw_data_record.manage',
    ANALYZE: 'raw_data_record.raw_data_record.analyze',
    LIST_READ: 'raw_data_record.list.read',
    STATS_READ: 'raw_data_record.stats.read',
    ARCHIVE: 'raw_data_record.raw_data_record.archive',
    BATCH_UPDATE: 'raw_data_record.raw_data_record.batch_update',
    DUPLICATE_READ: 'raw_data_record.duplicate.read'
  },

  // 数据处理管道模块
  DATA_PROCESSING_PIPELINE: {
    CREATE: 'data_processing.pipeline.create',
    READ: 'data_processing.pipeline.read',
    UPDATE: 'data_processing.pipeline.update',
    DELETE: 'data_processing.pipeline.delete',
    LIST_READ: 'data_processing.pipeline.read',
    STATS_READ: 'data_processing.pipeline.read',
    MANAGE: 'data_processing.pipeline.manage',
    ACTIVE_PIPELINES_READ: 'data_processing.pipeline.read'
  },

  // 快讯管理模块
  FLASH_NEWS: {
    READ: 'flash_news.flash_news.read',
    UPDATE: 'flash_news.flash_news.update',
    DELETE: 'flash_news.flash_news.delete',
    STATS: 'flash_news.flash_news.stats'
  },
} as const

// ==================== 角色常量 ====================
export const ROLES = {
  ADMIN: 'admin',
  ACCOUNT_MANAGER: 'account_manager',
  RISK_OFFICER: 'risk_officer',
  USER: 'user',
} as const 
